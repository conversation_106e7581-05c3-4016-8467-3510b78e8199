package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"google.golang.org/grpc"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/cache"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/tracer"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/vcxxjob"
	"new-gitlab.xunlei.cn/vcproject/backends/vcxxjob/controller"
	"new-gitlab.xunlei.cn/vcproject/backends/vcxxjob/mq"
)

func main() {
	config.Init()
	conf := config.GetServerConfig()
	logger.InitLoggerWitchLevel(conf.Logger, conf.LogLevel)
	logger.Infof("start vcxxjob config=%+v", util.JsonStr(conf))

	ctx, cancel := context.WithCancel(context.Background())
	if conf.Jaeger != nil && conf.Jaeger.Enable {
		prv, _ := tracer.InitTracer(ctx, conf.Jaeger)
		defer prv.Close(ctx)
	}

	svr := server.NewSvcServer(conf)
	svr.RegisterServices(func(s *grpc.Server) {
		vcxxjob.RegisterSServer(s, controller.NewController())
	})
	mq.InitNsq(conf)
	if env.IsLocal() || !env.IsProd() {
		go server.StartHttp(func(conn *grpc.ClientConn, mux *runtime.ServeMux) {
			vcxxjob.RegisterSHandlerClient(context.Background(), mux, vcxxjob.NewSClient(conn))
		})
	}
	go svr.Start()
	metric.InitPvUvReport(cache.GetRedisClient("vc_xxjob"), "xxjob")

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	sig := <-sigChan
	logger.Infof("received signal: %v starting graceful shutdown", sig)
	// 优雅退出流程
	// 1. 取消上下文，通知所有使用该上下文的 goroutine
	cancel()
	// 2. 停止接收新的请求并等待现有请求处理完成
	svr.GracefulStop()
	logger.Infof("server shutdown completed")

	//svcmgr.InitServices()
	//mq.InitNsq(conf)
	//cache.InitRedis(conf.Redis)

}
