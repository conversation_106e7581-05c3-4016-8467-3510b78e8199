package mq

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/dingding"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
	eventHandler "new-gitlab.xunlei.cn/vcproject/backends/baselib/nsqutil/event"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/vcxxjob"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/event"
	"new-gitlab.xunlei.cn/vcproject/backends/vcxxjob/service"
)

const (
	RvcOK int32 = 1
)

type TTSHandler struct {
}

func NewTTSHandler() *TTSHandler {
	return &TTSHandler{}
}

func (h *TTSHandler) HandleMessage(e eventHandler.Event) (err error) {
	logger.Debugf("HandleMessage eType=%+v", e.GetEType())
	switch e.GetEType() {
	case event.ETypeTTSInput:
		err = handleTextToSpeech(e)
	case event.ETypeRvcInput:
		err = handleVc(e)
	default:
	}
	return
}

func handleTextToSpeech(e eventHandler.Event) (err error) {
	eventData := event.TTSLineInputData{}
	tmpErr := json.Unmarshal(e.GetData(), &eventData)
	if tmpErr != nil {
		logger.Errorf("unmarshal event data error: %v msg=%+v", tmpErr, string(e.GetData()))
		return nil
	}
	logger.Infof("eventData=%+v", eventData)
	if eventData.LineID == 0 || len(eventData.LineText) == 0 {
		logger.Warnf("eventData invalid %+v", eventData)
		return
	}
	if len(eventData.ReferAudioUrl) > 0 && len(eventData.PromptText) == 0 {
		logger.Warnf("eventData promptText invalid = %+v", eventData)
		return
	}
	lineText := eventData.LineText
	svc := service.GetDialogueVCService()
	start := time.Now()
	ttsReq := &service.AsyncTTSReq{
		ReferAudioUrl: eventData.ReferAudioUrl,
		PromptText:    eventData.PromptText,
		Text:          lineText,
		TaskId:        eventData.TaskId,
	}
	logger.Infof("ttsReq=%+s", util.JsonStr(ttsReq))
	resp, tmpErr := svc.AsyncTextToSpeech(context.Background(), ttsReq, eventData.TTSEngine)
	duration := time.Since(start)
	if tmpErr != nil || resp.Code != 0 || resp.Data == nil || len(resp.Data.TaskId) == 0 {
		logger.Errorf("tmpErr=%+v lineText=%s line_id=%+v duration=%+v resp=%+v", tmpErr, lineText, eventData.LineID, duration.Seconds(), util.JsonStr(resp))
		alarmMsg := fmt.Sprintf("【%s】剧本参考音配音, TTS失败 \n text: %s \n character_id:%d \n assets_id=%d \n line_id=%d \n scirpt_id=%d \n err:%s \n resp:%s", env.GetEnvironment(), eventData.LineText, eventData.CharacterID, eventData.CharacterAssetID, eventData.LineID, eventData.ScriptID, util.JsonStr(tmpErr), util.JsonStr(resp))
		reportDingdingAlarm(alarmMsg)
		metric.ReportPv("dialogue_vc_failed", 1, prometheus.Labels{"step": "tts"})
		return nil
	}
	metric.HistogramLabelsWithBuckets("tts_latency", prometheus.Labels{}, []float64{2.0, 4.0, 6.0, 8.0, 12.0, 16.0, 24.0, 32.0, 40.0, 48.0, 64.0, 80.0, 100.0, 150.0, 200.0, 250.0, 300.0}).Observe(duration.Seconds())
	logger.Infof("resp=%+v lineID=%d duration=%+v", util.JsonStr(resp), eventData.LineID, duration.Seconds())
	return
}

// 处理变声
func handleVc(e eventHandler.Event) (err error) {
	eventData := event.RvcInputData{}
	tmpErr := json.Unmarshal(e.GetData(), &eventData)
	if tmpErr != nil {
		logger.Errorf("unmarshal event data error: %v msg=%+v", tmpErr, string(e.GetData()))
		return nil
	}
	logger.Infof("eventData=%+v", eventData)
	if len(eventData.TtsAudioUrl) == 0 || eventData.CharacterID == 0 || eventData.CharacterAssetID == 0 {
		logger.Errorf("eventData ttsAudioUrl empty %+v", eventData)
		return
	}
	svc := service.GetDialogueVCService()
	presetName := fmt.Sprintf("%d_%d", eventData.CharacterID, eventData.CharacterAssetID)
	if env.IsTest() {
		presetName = fmt.Sprintf("test_%d_%d", eventData.CharacterID, eventData.CharacterAssetID)
	}
	getPresetResp, tmpErr := svc.GetPresetByName(context.Background(), presetName)
	defer func() {
		if err != nil {
			logger.Errorf("handleVc err=%+v", tmpErr)
			updateErr := svc.UpdateVcTaskStatus(context.Background(), eventData.TaskId, map[string]interface{}{
				"status": vcxxjob.VcTaskStatus_STATUS_FAILED,
			})
			if updateErr != nil {
				logger.Errorf("UpdateVcTaskStatus err=%+v", updateErr)
			}
		}
	}()
	if tmpErr != nil || getPresetResp == nil || getPresetResp.Ret != RvcOK {
		err = fmt.Errorf("call getPresets err characterAssetsId=%d", eventData.CharacterAssetID)
		metric.ReportPv("dialogue_vc_failed", 1, prometheus.Labels{"step": "get_presets"})
		alarmMsg := fmt.Sprintf("【%s】剧本参考音配音, getPrest失败, characterId=%d characterAssetsId=%d lineID=%d scriptID=%d resp=%+v", env.GetEnvironment(), eventData.CharacterID, eventData.CharacterAssetID, eventData.LineID, eventData.ScriptID, util.JsonStr(getPresetResp))
		reportDingdingAlarm(alarmMsg)
		return
	}
	presetConfig := getPresetResp.Data
	if presetConfig == nil || len(presetConfig.PresetName) == 0 {
		err = fmt.Errorf("preset config not exist %s", presetName)
		metric.ReportPv("dialogue_vc_failed", 1, prometheus.Labels{"step": "get_presets"})
		alarmMsg := fmt.Sprintf("【%s】剧本参考音配音, getPrest失败, characterId=%d characterAssetsId=%d lineID=%d scriptID=%d presetName为空", env.GetEnvironment(), eventData.CharacterID, eventData.CharacterAssetID, eventData.LineID, eventData.ScriptID)
		reportDingdingAlarm(alarmMsg)
		return
	}
	if len(presetConfig.RVCName) == 0 {
		err = fmt.Errorf("preset config rvc_name not exist %s", presetName)
		metric.ReportPv("dialogue_vc_failed", 1, prometheus.Labels{"step": "get_presets"})
		alarmMsg := fmt.Sprintf("【%s】剧本参考音配音, getPrest失败, characterId=%d characterAssetsId=%d lineID=%d scriptID=%d rvcName为空", env.GetEnvironment(), eventData.CharacterID, eventData.CharacterAssetID, eventData.LineID, eventData.ScriptID)
		reportDingdingAlarm(alarmMsg)
		return
	}
	if len(presetConfig.ReferenceAudioURL) == 0 {
		err = fmt.Errorf("preset config refer_audio_url not exist %s", presetName)
		metric.ReportPv("dialogue_vc_failed", 1, prometheus.Labels{"step": "get_presets"})
		alarmMsg := fmt.Sprintf("【%s】剧本参考音配音, getPrest失败, characterId=%d characterAssetsId=%d lineID=%d scriptID=%d referenceAudioUrl为空", env.GetEnvironment(), eventData.CharacterID, eventData.CharacterAssetID, eventData.LineID, eventData.ScriptID)
		reportDingdingAlarm(alarmMsg)
		return
	}
	mid := fmt.Sprintf("%d", util.NowTimeMillis())
	if eventData.UseRvc {
		loadModelResp, tmpErr := svc.LoadModel(context.Background(), &service.LoadModelReq{
			RvcName:       presetConfig.RVCName,
			SeedName:      "whisper_small",
			ReferAudioUrl: presetConfig.ReferenceAudioURL,
			RequestId:     fmt.Sprintf("%s-%s", util.UUID(), mid),
			Mid:           mid,
		})
		if tmpErr != nil || loadModelResp == nil || loadModelResp.Ret != RvcOK {
			err = fmt.Errorf("call loadMode err rvcName=%s", presetConfig.RVCName)
			metric.ReportPv("dialogue_vc_failed", 1, prometheus.Labels{"step": "load_model"})
			alarmMsg := fmt.Sprintf("【%s】剧本参考音配音, loadModel失败, characterId=%d characterAssetsId=%d lineID=%d scriptID=%d resp=%+v", env.GetEnvironment(), eventData.CharacterID, eventData.CharacterAssetID, eventData.LineID, eventData.ScriptID, util.JsonStr(loadModelResp))
			reportDingdingAlarm(alarmMsg)
			return
		}
	}
	inferSvcResp, tmpErr := svc.InferVc(context.Background(), &service.InferRvcReq{
		RvcName:           presetConfig.RVCName,
		ReferenceAudioUrl: presetConfig.ReferenceAudioURL,
		SeedName:          "whisper_small",
		RequestId:         fmt.Sprintf("%s-%s", util.UUID(), mid),
		Mid:               mid,
		SourceAudioUrl:    eventData.TtsAudioUrl,
		CascadedUseRvc:    eventData.UseRvc,
	})
	if tmpErr != nil || inferSvcResp == nil || inferSvcResp.Ret != RvcOK {
		err = fmt.Errorf("call infer rvc err rvcName=%s", presetConfig.RVCName)
		metric.ReportPv("dialogue_vc_failed", 1, prometheus.Labels{"step": "infer_rvc"})
		alarmMsg := fmt.Sprintf("【%s】剧本参考音配音, inferRvc失败, characterId=%d characterAssetsId=%d lineID=%d scriptID=%d resp=%+v", env.GetEnvironment(), eventData.CharacterID, eventData.CharacterAssetID, eventData.LineID, eventData.ScriptID, util.JsonStr(inferSvcResp))
		reportDingdingAlarm(alarmMsg)
		return
	}
	finalRvcUrl := inferSvcResp.Data.FinalOutputUrl
	if len(finalRvcUrl) == 0 {
		err = fmt.Errorf("call infer rvc finalRvcUrl empty %s", presetConfig.PresetName)
		metric.ReportPv("dialogue_vc_failed", 1, prometheus.Labels{"step": "infer_rvc"})
		alarmMsg := fmt.Sprintf("【%s】剧本参考音配音, inferRvc失败, characterId=%d characterAssetsId=%d lineID=%d scriptID=%d 变声后url为空", env.GetEnvironment(), eventData.CharacterID, eventData.CharacterAssetID, eventData.LineID, eventData.ScriptID)
		reportDingdingAlarm(alarmMsg)
		return
	}
	tmpErr = svc.UpdateVcTaskStatus(context.Background(), eventData.TaskId, map[string]interface{}{
		"status":               vcxxjob.VcTaskStatus_STATUS_COMPLETED,
		"final_audio_url":      finalRvcUrl,
		"final_audio_duration": eventData.TtsAudioDuration,
	})
	if tmpErr != nil {
		logger.Errorf("UpdateVcTaskStatus err=%+v", tmpErr)
	}
	outputData := event.TTSLineOutputData{
		LineID:          eventData.LineID,
		DubbingDuration: int32(eventData.TtsAudioDuration),
		DubbingURL:      finalRvcUrl,
	}
	metric.ReportPv("dialogue_vc_succeed", 1, prometheus.Labels{})
	tmpErr = event.PushTTSLineOutput(outputData)
	if tmpErr != nil {
		logger.Errorf("PushTTSLineOutput err=%+v outputData=%+v", tmpErr, outputData)
	}
	return
}

func reportDingdingAlarm(dinddingMsg string) (err error) {
	// 发送钉钉
	dingding.RobotClient().Send(dingding.DingDingMsg{
		MsgType: dingding.DINGDING_MSG_TYPE_TEXT,
		Text: dingding.Text{
			Content: dinddingMsg,
		},
	})
	return
}
