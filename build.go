package main

import (
	"bytes"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"html/template"
	"log"
	"os"
	"os/exec"
	"regexp"
	"strings"
	"sync"
	"time"
)

var service = []string{
	"bizaccount",
	"bizmisc",
	"bizmoment",
	"bizstat",
	"biznotify",
	"bizactivity",
	"bizscript",

	"svcaccount",
	"svcmoment",
	"svcvc",
	"svcstat",
	"svcactivity",
	"svcscript",
	"svcchat",
	"svcreview",
	"svcconfig",
	"svcauth", // HTTP 签名验证服务

	"admin",
	"vcxxjob",
}

var (
	reset       bool
	action      string
	serviceList string
	tag         string
	product     string

	ProjectDir = os.Getenv("CI_PROJECT_DIR")
)

func init() {
	flag.BoolVar(&reset, "reset", false, "is build all")
	flag.StringVar(&action, "action", "build", "build/deploy")
	flag.StringVar(&serviceList, "service_list", "", "")
	flag.StringVar(&tag, "tag", "", "")
	flag.StringVar(&product, "product", "", product)
}

func main() {
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	flag.Parse()
	switch action {
	case "buildonly",
		"build":
		Build()
	}
}

func JsonStr(v interface{}) string {
	bs, _ := json.Marshal(v)
	return string(bs)
}

type AutoGenerated struct {
	Name              string `json:"name"`
	ProjectName       string `json:"projectName"`
	ProjectGroup      string `json:"projectGroup"`
	ProjectUpdateTime string `json:"projectUpdateTime"`
	Description       string `json:"description"`
	GitURL            string `json:"gitUrl"`
}

func Build() {
	log.Println(os.Getenv("CI_REGISTRY"))
	log.Println(os.Getenv("CI_REGISTRY_USER"))
	log.Println(os.Getenv("CI_REGISTRY_PASSWORD"))

	if os.Getenv("RESET") != "" {
		reset = true
	}
	specialService := ""
	if os.Getenv("SPECIAL") != "" {
		specialService = os.Getenv("SPECIAL")
	}

	log.Println(os.Getenv("CI_PROJECT_DIR"))

	db, err := Open(fileName)
	if err != nil {
		log.Panic(err)
	}
	defer db.Close()

	branch := os.Getenv("CI_COMMIT_REF_NAME")
	if len(tag) == 0 {
		tag = branch
	}

	compareBranch := branch
	versionRegexp := regexp.MustCompile(`^v\d+(\.\d+){2}$`)
	if versionRegexp.MatchString(branch) {
		compareBranch = "tag"
	}

	newCommitId := os.Getenv("CI_COMMIT_SHA")
	oldCommitId := db.Get(compareBranch)
	if reset {
		oldCommitId = ""
	}
	log.Printf("commitRef %s oldCommitId %s newCommitId %s", branch, oldCommitId, newCommitId)
	filter := make(map[string]struct{})
	changedService := make([]string, 0)
	if serviceList != "" {
		changedService = strings.Split(serviceList, ",")
	} else if specialService != "" {
		changedService = append(changedService, strings.Split(specialService, ",")...)
	} else {
		if oldCommitId == "" {
			changedService = service
		} else {
			diffFiles, err := GetGitDiffOutput(oldCommitId, newCommitId)
			if err != nil {
				log.Println(err.Error())
				log.Panic(err)
			}
			for _, file := range diffFiles {
				index := strings.Index(file, "/")
				if index != -1 {
					file = file[:index]
				}
				log.Printf("diffFiles file=%s\n", file)
				if (file == "go.mod" || file == "baselib" || file == "proto") && branch == "master" { // baselib 线上保证都编译，其他分支按对应服务下面引用，没有处罚到使用run pipeline 通过环境变量trigger
					changedService = service
					log.Println("base lib change build all")
					break
				} else {
					for _, s := range service {
						if _, ok := filter[s]; !ok {
							if file == s {
								filter[s] = struct{}{}
								changedService = append(changedService, s)
							}
						}
					}
				}
			}
		}
	}

	for _, s := range changedService {
		log.Printf("=== service %v need build ", s)
	}

	res, err := BuildServices(changedService)
	if !res {
		appendFailToShellConfig("/root/.bashrc", product)
		log.Printf("build error %v ", err)
		os.Exit(-1)
		return
	}
	// if action != "buildonly" {
	if len(changedService) != 0 {
		appendToShellConfig("/root/.bashrc", strings.Join(changedService, ","), product)
		log.Printf("appendToShellConfig changedService=%+v product=%s", strings.Join(changedService, ","), product)
	} else {
		appendToShellConfig("/root/.bashrc", "", product)
		log.Printf("appendToShellConfig product=%s", product)
	}
	// }
	if action != "buildonly" {
		// db.Set(branch, newCommitId)
		db.Set(compareBranch, newCommitId)

		bs, err := json.Marshal(db)
		if err != nil {
			log.Panic(bs)
		}
	}
}

func appendFailToShellConfig(configFile, product string) error {
	fileContent, err := os.ReadFile(configFile)
	if err != nil {
		log.Printf("appendToShellConfig err %v", err)
		return nil
	}

	pattern := regexp.MustCompile(`(?m)^\s*export\s*([^=]+)=(.*)$`)

	envVars := map[string]string{
		"PRODUCT":      product,
		"BUILD_STATUS": "fail",
	}

	result := pattern.ReplaceAllStringFunc(string(fileContent), func(match string) string {
		parts := pattern.FindStringSubmatch(match)
		if len(parts) == 3 {
			key := parts[1]
			if value, ok := envVars[key]; ok {
				return fmt.Sprintf("export %s=%s", key, value)
			}
		}
		return match // 如果变量名不存在，返回原始字符串
	})

	err = os.WriteFile(configFile, []byte(result), os.ModeAppend)
	if err != nil {
		log.Printf("appendToShellConfig err %v", err)
		return nil
	}
	return nil
}

func appendToShellConfig(configFile, line string, product string) error {
	fileContent, err := os.ReadFile(configFile)
	if err != nil {
		log.Printf("appendToShellConfig err %v", err)
		return nil
	}

	pattern := regexp.MustCompile(`(?m)^\s*export\s*([^=]+)=(.*)$`)

	envVars := map[string]string{
		"BUILD_SERVICES": line,
		"PRODUCT":        product,
		"BUILD_STATUS":   "success",
	}

	result := pattern.ReplaceAllStringFunc(string(fileContent), func(match string) string {
		parts := pattern.FindStringSubmatch(match)
		if len(parts) == 3 {
			key := parts[1]
			if value, ok := envVars[key]; ok {
				return fmt.Sprintf("export %s=%s", key, value)
			}
		}
		return match // 如果变量名不存在，返回原始字符串
	})
	err = os.WriteFile(configFile, []byte(result), os.ModeAppend)
	if err != nil {
		log.Printf("appendToShellConfig err %v", err)
		return nil
	}
	return nil
}

const (
	fileName = "/home/<USER>/.config/gl"
	fileDir  = "/home/<USER>/.config"
)

type Task struct {
	cmd       *exec.Cmd
	service   string
	input     string
	output    *bytes.Buffer
	startTime time.Time
	endTime   time.Time
	err       error
	isKill    bool
	//isWaitError bool
	w *Waiter
}

func (t *Task) Start() (err error) {
	t.w.Wait()
	return t.cmd.Start()
}

func (t *Task) Wait() (err error) {
	err = t.cmd.Wait()
	t.w.Done()
	return
}

type TaskGroup struct {
	tasks []*Task
	stop  bool
	mu    sync.Mutex
}

func (t *TaskGroup) buildCmd(service string, cmdTemplate string) *Task {
	tem, err := template.New("").Parse(cmdTemplate)
	if err != nil {
		log.Panic(err)
	}
	var curTag string
	curTag = tag
	if strings.Contains(tag, "test") {
		curTag = "test"
	}
	input := &bytes.Buffer{}
	err = tem.Execute(input, map[string]string{
		"service": service,
		"tag":     curTag,
		"product": product,
	})
	if err != nil {
		log.Panic(err)
	}
	return t.runCmd(service, input.String())
}

func (r *TaskGroup) runCmd(name string, input string) *Task {
	cmd := exec.Command("sh")
	cmd.Stdin = bytes.NewBufferString(input)
	out := &bytes.Buffer{}
	cmd.Stdout = out
	cmd.Stderr = out
	task := &Task{
		cmd:       cmd,
		service:   name,
		input:     input,
		output:    out,
		startTime: time.Now(),
	}
	return task
}

func (t *TaskGroup) WaitAll() bool {
	wg := sync.WaitGroup{}
	waiter := NewWaiter(6)
	for _, v := range t.tasks {
		wg.Add(1)
		task := v
		go func() {
			waiter.Wait()
			defer waiter.Done()
			defer wg.Done()
			err := task.cmd.Start()
			if err != nil {
				task.err = err
				t.StopAll()
				return
			}
			err = task.cmd.Wait()
			if err != nil {
				task.err = err
				t.StopAll()
			} else {
				task.endTime = time.Now()
			}
		}()
	}

	wg.Wait()
	result := true
	for _, task := range t.tasks {
		if task.err != nil {
			result = false
			log.Printf("service %20s tag %v run fail, output %s error %v", task.service, tag, task.output.String(), task.err)
		} else {
			log.Printf("service %20s tag %v run success, timeCost %f s", task.service, tag, task.endTime.Sub(task.startTime).Seconds())
		}
	}
	return result
}

func (t *TaskGroup) StopAll() {
	t.mu.Lock()
	defer t.mu.Unlock()
	if t.stop {
		return
	}
	for _, task := range t.tasks[1:] {
		task.isKill = true
		if task.cmd.Process != nil {
			task.cmd.Process.Kill()
		}
	}
	t.stop = true
}

func fileExist(file string) (bool, error) {
	_, err := os.Stat(fileName)
	if err == nil {
		return true, nil
	} else if errors.Is(err, os.ErrNotExist) {
		return false, nil
	} else {
		return false, err
	}
}

var cmdAdminTemplate = `
set -e
pwd
cd {{.service}}
NAME={{.service}}
go env -w GO111MODULE=on && go env -w CGO_ENABLED=0 && go build -o bin/vc.{{.service}}.s
IMAGE_NAME="${CI_REGISTRY}/vcproject/backends/{{.service}}"
docker build ./ -f Dockerfile -t ${IMAGE_NAME}:{{.tag}}
docker login ${CI_REGISTRY} -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
docker push ${IMAGE_NAME}:{{.tag}}
docker rmi ${IMAGE_NAME}:{{.tag}}
updateTime=$(date "+%Y-%m-%d %H:%M:%S")
echo '
{
    "tag_time": "'${updateTime}'",
    "product": "'{{.product}}'",
    "git_url": "'https://new-gitlab.xunlei.cn/vcproject/{{.service}}'",
    "service_name": "'{{.service}}'",
    "service_description": "'{{.service}}'",
    "version_description": "'{{.service}}'",
    "value": "'{{.tag}}'",
    "group_name": "vcproject"
}' | curl -s --location 'https://xops.office.k8s.xunlei.cn/api/v1/public/service/version' \
        --header 'Content-Type: application/json' \
        --data @- | { cat; echo ""; }

echo "build {{.service}}.{{.tag}} success"
`

// ${CI_COMMIT_REF_NAME}
var cmdTemplate = `
set -e
pwd
mkdir -p {{.service}}/deploy
cp -f proto/api/{{.service}}/{{.service}}.pb {{.service}}/deploy/proto.pb
cd {{.service}}
NAME={{.service}}
go build --ldflags '-extldflags -static' -o bin/vc.{{.service}}.s
IMAGE_NAME="${CI_REGISTRY}/vcproject/backends/{{.service}}"
INIT_IMAGE_NAME="${IMAGE_NAME}/{{.service}}-initdata"
docker build ./ -f Dockerfile -t ${IMAGE_NAME}:{{.tag}}
docker build ./ -f Dockerfile.initdata -t ${INIT_IMAGE_NAME}:{{.tag}}
docker login ${CI_REGISTRY} -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
docker push ${IMAGE_NAME}:{{.tag}}
docker push ${INIT_IMAGE_NAME}:{{.tag}}
docker rmi ${IMAGE_NAME}:{{.tag}}
docker rmi ${INIT_IMAGE_NAME}:{{.tag}}

updateTime=$(date "+%Y-%m-%d %H:%M:%S")
echo '
{
    "tag_time": "'${updateTime}'",
    "product": "'{{.product}}'",
    "git_url": "'https://new-gitlab.xunlei.cn/vcproject/{{.service}}'",
    "service_name": "'{{.service}}'",
    "service_description": "'{{.service}}'",
    "version_description": "'{{.service}}'",
    "value": "'{{.tag}}'",
    "group_name": "vcproject"
}' | curl -s --location 'https://xops.office.k8s.xunlei.cn/api/v1/public/service/version' \
        --header 'Content-Type: application/json' \
        --data @- | { cat; echo ""; }

echo "build {{.service}}.{{.tag}} success"
`

var httpTemplate = `
set -e
pwd
mkdir -p {{.service}}/deploy
cd {{.service}}
NAME={{.service}}
go build --ldflags '-extldflags -static' -o bin/vc.{{.service}}.s
IMAGE_NAME="${CI_REGISTRY}/vcproject/backends/{{.service}}"
docker build ./ -f Dockerfile -t ${IMAGE_NAME}:{{.tag}}
docker login ${CI_REGISTRY} -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
docker push ${IMAGE_NAME}:{{.tag}}
docker rmi ${IMAGE_NAME}:{{.tag}}
updateTime=$(date "+%Y-%m-%d %H:%M:%S")
echo '
{
    "tag_time": "'${updateTime}'",
    "product": "'{{.product}}'",
    "git_url": "'https://new-gitlab.xunlei.cn/vcproject/{{.service}}'",
    "service_name": "'{{.service}}'",
    "service_description": "'{{.service}}'",
    "version_description": "'{{.service}}'",
    "value": "'{{.tag}}'",
    "group_name": "vcproject"
}' | curl -s --location 'https://xops.office.k8s.xunlei.cn/api/v1/public/service/version' \
        --header 'Content-Type: application/json' \
        --data @- | { cat; echo ""; }

echo "build {{.service}}.{{.tag}} success"
`

var buildOnlyTemplate = `
set -e
pwd
cd {{.service}}
go build --ldflags '-extldflags -static' -o bin/vc.{{.service}}.s
echo "build {{.service}} {{.tag}} pass"
`

func BuildServices(service []string) (res bool, err error) {
	tg := TaskGroup{
		tasks: nil,
		stop:  false,
		mu:    sync.Mutex{},
	}

	for _, s := range service {
		if action == "buildonly" {
			tg.tasks = append(tg.tasks, tg.buildCmd(s, buildOnlyTemplate))
		} else {
			if s == "admin" {
				tg.tasks = append(tg.tasks, tg.buildCmd(s, cmdAdminTemplate))
			} else if s == "biznotify" || s == "jobmonitor" || s == "svcauth" {
				tg.tasks = append(tg.tasks, tg.buildCmd(s, httpTemplate))
			} else {
				tg.tasks = append(tg.tasks, tg.buildCmd(s, cmdTemplate))
			}
		}
	}
	res = tg.WaitAll()
	if !res {
		return
	}
	return
}

func GetGitDiffOutput(oldCommit, newCommit string) (result []string, err error) {
	p := exec.Command("git", "diff", "--name-only", oldCommit, newCommit)
	out := &bytes.Buffer{}
	p.Stdout = out
	p.Stdin = out
	err = p.Run()
	if err != nil {
		return nil, err
	}
	log.Printf("git diff --name-only %s %s", oldCommit, newCommit)
	log.Println("git diff out:\n", out.String())
	return strings.Split(out.String(), "\n"), nil
}

type Db struct {
	file string
	db   map[string]string
}

func Open(fileName string) (*Db, error) {
	os.MkdirAll(fileDir, 0x777)
	exist, err := fileExist(fileName)
	if err != nil {
		log.Panic(err)
	}
	var (
		db map[string]string = make(map[string]string)
	)
	if exist {
		log.Printf("====read filename==== %v", fileName)
		content, err := os.ReadFile(fileName)
		if err != nil {
			log.Panic(err)
		}
		err = json.Unmarshal(content, &db)
		if err != nil {
			err = os.WriteFile(fileName, []byte(""), 0755)
			if err != nil {
				log.Panic(err)
			}
		}
	} else {
		log.Printf("====create filename==== %v", fileName)
		file, err := os.Create(fileName)
		if err != nil {
			log.Panic(err)
		}
		_, err = file.Write([]byte("{}"))
		if err != nil {
			log.Panic(err)
		}
	}
	return &Db{
		file: fileName,
		db:   db,
	}, nil
}

func (d *Db) Set(key, value string) {
	d.db[key] = value
}

func (d *Db) Get(key string) string {
	return d.db[key]
}

func (d *Db) Close() error {
	bs, err := json.Marshal(d.db)
	if err != nil {
		log.Panic(err)
	}
	err = os.WriteFile(d.file, bs, 06444)
	if err != nil {
		log.Panic(err)
	}
	return nil
}

func NewWaiter(max int) *Waiter {
	w := &Waiter{
		mu:   sync.Mutex{},
		cond: nil,
		num:  0,
		max:  max,
	}
	w.cond = sync.NewCond(&(w.mu))
	return w
}

type Waiter struct {
	mu   sync.Mutex
	cond *sync.Cond
	num  int
	max  int
}

func (w *Waiter) Wait() {
	w.mu.Lock()
	defer w.mu.Unlock()
	for w.num >= w.max {
		w.cond.Wait()
	}
	w.num++
}

func (w *Waiter) Done() {
	w.mu.Lock()
	defer w.mu.Unlock()
	w.num--
	w.cond.Broadcast()
}
