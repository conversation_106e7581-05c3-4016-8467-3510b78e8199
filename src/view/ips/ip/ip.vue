
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
        <el-form-item label="IP名字" prop="name">
          <el-input v-model="searchInfo.name" placeholder="请输入IP名" clearable />
        </el-form-item>

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <!--
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            -->
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        >
        <el-table-column type="selection" width="55" />
        
            <el-table-column align="left" label="IPID" prop="id" width="120" />

            <el-table-column align="left" label="IP名称" prop="name" width="200" />

            <el-table-column align="left" label="IP描述" prop="description" width="200" />

            <!-- 
               <el-table-column align="left" label="IP logo" prop="logo" width="120" />  
            -->
            <el-table-column align="left" label="IP logo" width="120">
              <template #default="scope">
                <el-image
                  v-if="scope.row.logo"
                  style="width: 100%; height: 120px"
                  :src="scope.row.logo"
                  :preview-src-list="[scope.row.logo]"
                  fit="contain"
                  :preview-teleported="true"
                />
                <span v-else class="no-image-resource">无图片资源</span>
              </template>
            </el-table-column>

            <el-table-column align="left" label="IP热度值" prop="hot" width="120" />

            <el-table-column align="left" label="排序值" prop="sort" width="120" />

            <el-table-column align="left" label="创建时间" prop="created_at" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>

            <el-table-column align="left" label="更新时间" prop="updated_at" width="180">
              <template #default="scope">
                  {{ formatDate(scope.row.updated_at) }}
                </template>
            </el-table-column>

        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <!--
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            -->
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateIpsFunc(scope.row)">编辑</el-button>
            <!-- 
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            -->
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[20, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            <el-form-item label="IPID:" prop="id" v-if="type === 'update'">
              <el-input 
              v-model.number="formData.id" 
              :clearable="true" 
              placeholder="请输入IPID" 
              :disabled="type ==='update'"
              v-if="type === 'update'"
            />
</el-form-item>
            <el-form-item label="IP名称:" prop="name">
    <el-input v-model="formData.name" :clearable="true" placeholder="请输入IP名称" />
</el-form-item>
            <el-form-item label="IP描述:" prop="description">
    <el-input v-model="formData.description" :clearable="true" placeholder="请输入IP描述" />
</el-form-item>
            <el-form-item label="IP logo:" prop="logo">
    <el-input v-model="formData.logo" :clearable="true" placeholder="请输入IP logo" />
    <div class="logo-upload-container">
        <el-upload
            class="logo-uploader"
            drag
            :show-file-list="false"
            :before-upload="beforeLogoUpload"
            :http-request="handleLogoUpload"
        >
            <el-image
                v-if="formData.logo"
                :src="formData.logo"
                class="logo-preview"
                fit="contain"
            />
            <el-icon v-else class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
                拖拽图片到此处或 <em>点击上传</em>
            </div>
        </el-upload>
    </div>
</el-form-item>
            <el-form-item label="IP热度值:" prop="hot">
    <el-input v-model.number="formData.hot" :clearable="true" placeholder="请输入IP热度值" />
</el-form-item>
            <el-form-item label="排序值:" prop="sort">
    <el-input v-model.number="formData.sort" :clearable="true" placeholder="请输入排序值" />
</el-form-item>
          </el-form>
    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="IPID">
    {{ detailFrom.id }}
</el-descriptions-item>
                    <el-descriptions-item label="IP名称">
    {{ detailFrom.name }}
</el-descriptions-item>
                    <el-descriptions-item label="IP描述">
    {{ detailFrom.description }}
</el-descriptions-item>
                    <el-descriptions-item label="IP logo">
                        <el-image
                          v-if="detailFrom.logo"
                          :src="detailFrom.logo"
                          style="width: 200px; height: 200px;"
                          fit="contain"
                          :preview-src-list="[detailFrom.logo]"
                          :preview-teleported="true"
                        />
                        <span v-else class="no-image-resource">无图片资源</span>
</el-descriptions-item>
                    <el-descriptions-item label="IP热度值">
    {{ detailFrom.hot }}
</el-descriptions-item>
                    <el-descriptions-item label="排序值">
    {{ detailFrom.sort }}
</el-descriptions-item>
                    <el-descriptions-item label="创建时间">
    {{ formatDate(detailFrom.created_at) }}
</el-descriptions-item>
                    <el-descriptions-item label="更新时间">
    {{ formatDate(detailFrom.updated_at) }}
</el-descriptions-item>
            </el-descriptions>
        </el-drawer>

  </div>
</template>

<script setup>
import {
  createIps,
  deleteIp,
  deleteIpsByIds,
  updateIps,
  findIps,
  getIpsList
} from '@/api/ips/ip'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { ref, reactive } from 'vue'
import { useAppStore } from "@/pinia"
import {uploadToOSS} from "@/utils/oss"



defineOptions({
    name: 'Ips'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(true)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            id: undefined,
            name: '',
            description: '',
            logo: '',
            hot: undefined,
            sort: undefined,
            created_at: undefined,
            updated_at: undefined,
        })



// 验证规则
const rule = reactive({
})

const searchRule = reactive({
  CreatedAt: [
    { validator: (rule, value, callback) => {
      if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
        callback(new Error('请填写结束日期'))
      } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
        callback(new Error('请填写开始日期'))
      } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
        callback(new Error('开始日期应当早于结束日期'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(20)
const tableData = ref([])
const searchInfo = ref({})
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getIpsList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
// const deleteRow = (row) => {
//     ElMessageBox.confirm('确定要删除吗?', '提示', {
//         confirmButtonText: '确定',
//         cancelButtonText: '取消',
//         type: 'warning'
//     }).then(() => {
//             deleteIpsFunc(row)
//         })
//     }

// 多选删除
// const onDelete = async() => {
//   ElMessageBox.confirm('确定要删除吗?', '提示', {
//     confirmButtonText: '确定',
//     cancelButtonText: '取消',
//     type: 'warning'
//   }).then(async() => {
//       const ids = []
//       if (multipleSelection.value.length === 0) {
//         ElMessage({
//           type: 'warning',
//           message: '请选择要删除的数据'
//         })
//         return
//       }
//       multipleSelection.value &&
//         multipleSelection.value.map(item => {
//           ids.push(item.id)
//         })
//       const res = await deleteIpsByIds({ ids })
//       if (res.code === 0) {
//         ElMessage({
//           type: 'success',
//           message: '删除成功'
//         })
//         if (tableData.value.length === ids.length && page.value > 1) {
//           page.value--
//         }
//         getTableData()
//       }
//       })
//     }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateIpsFunc = async(row) => {
    const res = await findIps({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteIpsFunc = async (row) => {
    const res = await deleteIp({ id: row.id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        id: undefined,
        name: '',
        description: '',
        logo: '',
        hot: undefined,
        sort: undefined,
        created_at: undefined
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createIps(formData.value)
                  break
                case 'update':
                  res = await updateIps(formData.value)
                  break
                default:
                  res = await createIps(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findIps({ id: row.id })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}


// 上传logo
// 上传前校验
const beforeLogoUpload = (file) => {
    const isImage = file.type.startsWith('image/')
    const isLt10M = file.size / 1024 / 1024 < 10

    if (!isImage) {
        ElMessage.error('只能上传图片文件!')
        return false
    }
    if (!isLt10M) {
        ElMessage.error('图片大小不能超过 10M!')
        return false
    }
    return true
}

// 处理图片上传
const handleLogoUpload = async (options) => {
    try {
        const res = await uploadToOSS(options.file, 'image', 'ips/log')
        formData.value.logo = res.url // 假设返回的数据中包含图片URL
        ElMessage.success('上传成功')
    } catch (error) {
        ElMessage.error('上传失败')
    }
}

</script>

<style scoped>
.logo-upload-container {
    width: 100%;
}
.logo-uploader {
    width: 100%;
}
.logo-preview {
    width: 200px;
    height: 200px;
    display: block;
    margin: 0 auto;
}

/* 无图片资源样式 */
.no-image-resource {
    color: #C0C4CC;
    font-style: italic;
    font-size: 12px;
}
</style>
