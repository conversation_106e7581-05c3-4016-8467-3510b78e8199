
<template>
    <div>
      <div class="gva-search-box">
        <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
  
          <template v-if="showAllQuery">
            <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
          </template>
  
          <el-form-item>
            <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
            <el-button icon="refresh" @click="onReset">重置</el-button>
            <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
            <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="gva-table-box">
          <!--
          <div class="gva-btn-list">
              <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
              <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
          </div>
          -->
          <el-table
          ref="multipleTable"
          style="width: 100%"
          tooltip-effect="dark"
          :data="tableData"
          row-key="id"
          @selection-change="handleSelectionChange"
          >
          <el-table-column type="selection" width="55" />

              <el-table-column align="left" label="朋友圈ID" prop="id" width="100" />

              <el-table-column align="left" label="用户ID" prop="userId" width="100" />

              <el-table-column align="left" label="角色ID" prop="characterId" width="100" />

              <el-table-column align="left" label="原声音频" prop="originAudioUrl" width="280">
                <template #default="scope">
                  <div class="audio-container">
                    <audio
                      v-if="scope.row.originAudioUrl"
                      controls
                      :src="scope.row.originAudioUrl"
                      style="width: 240px; height: 32px;"
                    >
                      您的浏览器不支持音频播放
                    </audio>
                    <span v-else class="no-audio-resource">无音频</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column align="left" label="变声音频" prop="voiceAudioUrl" width="280">
                <template #default="scope">
                  <div class="audio-container">
                    <audio
                      v-if="scope.row.voiceAudioUrl"
                      controls
                      :src="scope.row.voiceAudioUrl"
                      style="width: 240px; height: 32px;"
                    >
                      您的浏览器不支持音频播放
                    </audio>
                    <span v-else class="no-audio-resource">无音频</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column align="left" label="文本内容" prop="content" width="200">
                <template #default="scope">
                  <el-tooltip :content="scope.row.content" placement="top" :disabled="!scope.row.content || scope.row.content.length <= 20">
                    <span class="content-text">{{ scope.row.content || '-' }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>

              <el-table-column align="left" label="ASR数据" prop="asrDataUrl" width="120">
                <template #default="scope">
                  <el-tooltip :content="scope.row.asrDataUrl" placement="top" :disabled="!scope.row.asrDataUrl">
                    <span class="url-text">{{ scope.row.asrDataUrl ? '有数据' : '无数据' }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>

              <el-table-column align="left" label="识别状态" prop="asrStatus" width="120">
                <template #default="scope">
                  <el-tag :type="getAsrStatusType(scope.row.asrStatus)" size="small">
                    {{ getAsrStatusText(scope.row.asrStatus) }}
                  </el-tag>
                </template>
              </el-table-column>

             
  
              <el-table-column align="left" label="创建时间" prop="createdAt" width="160" >
                <template #default="scope">
                {{ formatDate(scope.row.createdAt) }}
              </template>
              </el-table-column>

              <el-table-column align="left" label="更新时间" prop="updatedAt" width="160" >
                <template #default="scope">
                {{ formatDate(scope.row.updatedAt) }}
              </template>
              </el-table-column>
  
          <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
              <template #default="scope">
              <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
              <el-button  type="primary" link icon="edit" class="table-button" @click="updateMomentsFunc(scope.row)">编辑</el-button>
              <!--
              <el-button  type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
              -->
              </template>
          </el-table-column>
          </el-table>
          <div class="gva-pagination">
              <el-pagination
              layout="total, sizes, prev, pager, next, jumper"
              :current-page="page"
              :page-size="pageSize"
              :page-sizes="[10, 30, 50, 100]"
              :total="total"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
              />
          </div>
      </div>
      <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
         <template #header>
                <div class="flex justify-between items-center">
                  <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                  <div>
                    <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                    <el-button @click="closeDialog">取 消</el-button>
                  </div>
                </div>
              </template>
  
            <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
              <el-form-item label="朋友圈ID:" prop="id">
      <el-input v-model.number="formData.id" :clearable="true" placeholder="请输入朋友圈ID" />
  </el-form-item>
              <el-form-item label="用户ID:" prop="userId">
      <el-input v-model.number="formData.userId" :clearable="true" placeholder="请输入用户ID" />
  </el-form-item>
              <el-form-item label="角色ID:" prop="characterId">
      <el-input v-model.number="formData.characterId" :clearable="true" placeholder="请输入角色ID" />
  </el-form-item>
              <el-form-item label="原声音频URL:" prop="originAudioUrl">
      <el-input v-model="formData.originAudioUrl" :clearable="true" placeholder="请输入原声音频URL" />
  </el-form-item>
              <el-form-item label="变声音频URL:" prop="voiceAudioUrl">
      <el-input v-model="formData.voiceAudioUrl" :clearable="true" placeholder="请输入变声音频URL" />
  </el-form-item>
              <el-form-item label="文本内容:" prop="content">
      <el-input v-model="formData.content" :clearable="true" placeholder="请输入文本内容" />
  </el-form-item>
              <el-form-item label="ASR数据地址:" prop="asrDataUrl">
      <el-input v-model="formData.asrDataUrl" :clearable="true" placeholder="请输入ASR数据地址" />
  </el-form-item>
              <el-form-item label="asr识别状态 0:识别中 1:识别成功 2:识别失败:" prop="asrStatus">
      <el-input v-model.number="formData.asrStatus" :clearable="true" placeholder="请输入状态" />
  </el-form-item>
              <el-form-item label="语音时长(秒):" prop="duration">
      <el-input v-model.number="formData.duration" :clearable="true" placeholder="请输入语音时长(秒)" />
  </el-form-item>
  <!--
              <el-form-item label="创建时间:" prop="createdAt">
      <el-input v-model.number="formData.createdAt" :clearable="true" placeholder="请输入创建时间" />
  </el-form-item>
              <el-form-item label="更新时间:" prop="updatedAt">
      <el-input v-model.number="formData.updatedAt" :clearable="true" placeholder="请输入更新时间" />
  </el-form-item>
-->
            </el-form>
      </el-drawer>
  
      <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
              <el-descriptions :column="1" border>
                      <el-descriptions-item label="朋友圈ID">
      {{ detailFrom.id }}
  </el-descriptions-item>
                      <el-descriptions-item label="用户ID">
      {{ detailFrom.userId }}
  </el-descriptions-item>
                      <el-descriptions-item label="角色ID">
      {{ detailFrom.characterId }}
  </el-descriptions-item>
                      <el-descriptions-item label="原声音频">
                        <div v-if="detailFrom.originAudioUrl">
                          <audio
                            controls
                            :src="detailFrom.originAudioUrl"
                            style="width: 300px; height: 32px; margin-bottom: 8px;"
                          >
                            您的浏览器不支持音频播放
                          </audio>
                          <div style="font-size: 12px; color: #666;">
                            {{ detailFrom.originAudioUrl }}
                          </div>
                        </div>
                        <span v-else class="no-audio-resource">无音频</span>
  </el-descriptions-item>
                      <el-descriptions-item label="变声音频">
                        <div v-if="detailFrom.voiceAudioUrl">
                          <audio
                            controls
                            :src="detailFrom.voiceAudioUrl"
                            style="width: 300px; height: 32px; margin-bottom: 8px;"
                          >
                            您的浏览器不支持音频播放
                          </audio>
                          <div style="font-size: 12px; color: #666;">
                            {{ detailFrom.voiceAudioUrl }}
                          </div>
                        </div>
                        <span v-else class="no-audio-resource">无音频</span>
  </el-descriptions-item>
                      <el-descriptions-item label="文本内容">
      {{ detailFrom.content }}
  </el-descriptions-item>
                      <el-descriptions-item label="ASR数据地址">
      {{ detailFrom.asrDataUrl }}
  </el-descriptions-item>
                      <el-descriptions-item label="识别状态">
                        <el-tag :type="getAsrStatusType(detailFrom.asrStatus)" size="small">
                          {{ getAsrStatusText(detailFrom.asrStatus) }}
                        </el-tag>
  </el-descriptions-item>
                      <el-descriptions-item label="语音时长">
                        {{ detailFrom.duration ? formatDuration(detailFrom.duration) : '-' }}
  </el-descriptions-item>
                      <el-descriptions-item label="创建时间">
                        {{ formatDate(detailFrom.createdAt) }}
  </el-descriptions-item>
                      <el-descriptions-item label="更新时间">
                        {{ formatDate(detailFrom.updatedAt) }}
  </el-descriptions-item>
              </el-descriptions>
          </el-drawer>
  
    </div>
  </template>
  
  <script setup>
  import {
    createMoments,
    deleteMoments,
    deleteMomentsByIds,
    updateMoments,
    findMoments,
    getMomentsList
  } from '@/api/mymoments/moments'
  
  // 全量引入格式化工具 请按需保留
  import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ref, reactive } from 'vue'
  import { useAppStore } from "@/pinia"
  
  
  
  
  defineOptions({
      name: 'Moments'
  })
  
  // 提交按钮loading
  const btnLoading = ref(false)
  const appStore = useAppStore()
  
  // 控制更多查询条件显示/隐藏状态
  const showAllQuery = ref(true)
  
  // 自动化生成的字典（可能为空）以及字段
  const formData = ref({
              id: undefined,
              userId: undefined,
              characterId: undefined,
              originAudioUrl: '',
              voiceAudioUrl: '',
              content: '',
              asrDataUrl: '',
              asrStatus: false,
              duration: undefined,
              createdAt: undefined,
              updatedAt: undefined,
          })
  
  
  
  // 验证规则
  const rule = reactive({
  })
  
  const searchRule = reactive({
    CreatedAt: [
      { validator: (rule, value, callback) => {
        if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
          callback(new Error('请填写结束日期'))
        } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
          callback(new Error('请填写开始日期'))
        } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
          callback(new Error('开始日期应当早于结束日期'))
        } else {
          callback()
        }
      }, trigger: 'change' }
    ],
  })
  
  const elFormRef = ref()
  const elSearchFormRef = ref()
  
  // =========== 表格控制部分 ===========
  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(10)
  const tableData = ref([])
  const searchInfo = ref({})
  // 重置
  const onReset = () => {
    searchInfo.value = {}
    getTableData()
  }
  
  // 搜索
  const onSubmit = () => {
    elSearchFormRef.value?.validate(async(valid) => {
      if (!valid) return
      page.value = 1
      if (searchInfo.value.asrStatus === ""){
          searchInfo.value.asrStatus=null
      }
      getTableData()
    })
  }
  
  // 分页
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }
  
  // 修改页面容量
  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }
  
  // 查询
  const getTableData = async() => {
    const table = await getMomentsList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
    if (table.code === 0) {
      tableData.value = table.data.list
      total.value = table.data.total
      page.value = table.data.page
      pageSize.value = table.data.pageSize
    }
  }
  
  getTableData()
  
  // ============== 表格控制部分结束 ===============
  
  // 获取需要的字典 可能为空 按需保留
  const setOptions = async () =>{
  }
  
  // 获取需要的字典 可能为空 按需保留
  setOptions()
  
  
  // 多选数据
  const multipleSelection = ref([])
  // 多选
  const handleSelectionChange = (val) => {
      multipleSelection.value = val
  }
  
  // 删除行
  const deleteRow = (row) => {
      ElMessageBox.confirm('确定要删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
      }).then(() => {
              deleteMomentsFunc(row)
          })
      }
  
  // 多选删除
  const onDelete = async() => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async() => {
        const ids = []
        if (multipleSelection.value.length === 0) {
          ElMessage({
            type: 'warning',
            message: '请选择要删除的数据'
          })
          return
        }
        multipleSelection.value &&
          multipleSelection.value.map(item => {
            ids.push(item.id)
          })
        const res = await deleteMomentsByIds({ ids })
        if (res.code === 0) {
          ElMessage({
            type: 'success',
            message: '删除成功'
          })
          if (tableData.value.length === ids.length && page.value > 1) {
            page.value--
          }
          getTableData()
        }
        })
      }
  
  // 行为控制标记（弹窗内部需要增还是改）
  const type = ref('')
  
  // 更新行
  const updateMomentsFunc = async(row) => {
      const res = await findMoments({ id: row.id })
      type.value = 'update'
      if (res.code === 0) {
          formData.value = res.data
          dialogFormVisible.value = true
      }
  }
  
  
  // 删除行
  const deleteMomentsFunc = async (row) => {
      const res = await deleteMoments({ id: row.id })
      if (res.code === 0) {
          ElMessage({
                  type: 'success',
                  message: '删除成功'
              })
              if (tableData.value.length === 1 && page.value > 1) {
              page.value--
          }
          getTableData()
      }
  }
  
  // 弹窗控制标记
  const dialogFormVisible = ref(false)
  
  // 打开弹窗
  const openDialog = () => {
      type.value = 'create'
      dialogFormVisible.value = true
  }
  
  // 关闭弹窗
  const closeDialog = () => {
      dialogFormVisible.value = false
      formData.value = {
          id: undefined,
          userId: undefined,
          characterId: undefined,
          originAudioUrl: '',
          voiceAudioUrl: '',
          content: '',
          asrDataUrl: '',
          asrStatus: false,
          duration: undefined,
          createdAt: undefined,
          updatedAt: undefined,
          }
  }
  // 弹窗确定
  const enterDialog = async () => {
       btnLoading.value = true
       elFormRef.value?.validate( async (valid) => {
               if (!valid) return btnLoading.value = false
                let res
                switch (type.value) {
                  case 'create':
                    res = await createMoments(formData.value)
                    break
                  case 'update':
                    res = await updateMoments(formData.value)
                    break
                  default:
                    res = await createMoments(formData.value)
                    break
                }
                btnLoading.value = false
                if (res.code === 0) {
                  ElMessage({
                    type: 'success',
                    message: '创建/更改成功'
                  })
                  closeDialog()
                  getTableData()
                }
        })
  }
  
  const detailFrom = ref({})
  
  // 查看详情控制标记
  const detailShow = ref(false)
  
  
  // 打开详情弹窗
  const openDetailShow = () => {
    detailShow.value = true
  }
  
  
  // 打开详情
  const getDetails = async (row) => {
    // 打开弹窗
    const res = await findMoments({ id: row.id })
    if (res.code === 0) {
      detailFrom.value = res.data
      openDetailShow()
    }
  }
  
  
  // 关闭详情弹窗
  const closeDetailShow = () => {
    detailShow.value = false
    detailFrom.value = {}
  }

  // 获取ASR状态文本
  const getAsrStatusText = (status) => {
    const statusMap = {
      0: '识别中',
      1: '识别成功',
      2: '识别失败'
    }
    return statusMap[status] || '未知'
  }

  // 获取ASR状态标签类型
  const getAsrStatusType = (status) => {
    const typeMap = {
      0: 'warning',  // 识别中 - 黄色
      1: 'success',  // 识别成功 - 绿色
      2: 'danger'    // 识别失败 - 红色
    }
    return typeMap[status] || 'info'
  }

  // 格式化时长显示
  const formatDuration = (seconds) => {
    if (!seconds || seconds <= 0) return '0秒'

    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)

    if (hours > 0) {
      return `${hours}时${minutes}分${secs}秒`
    } else if (minutes > 0) {
      return `${minutes}分${secs}秒`
    } else {
      return `${secs}秒`
    }
  }
  
  
  </script>
  
  <style scoped>
  /* 音频容器样式 */
  .audio-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .audio-duration {
    font-size: 11px;
    color: #909399;
    text-align: center;
  }

  .no-audio-resource {
    color: #C0C4CC;
    font-style: italic;
    font-size: 12px;
    text-align: center;
    padding: 8px 0;
  }

  /* 文本内容样式 */
  .content-text {
    display: block;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }

  /* URL文本样式 */
  .url-text {
    color: #606266;
    font-size: 12px;
  }

  /* 表格行高优化 */
  :deep(.el-table__row) {
    height: auto;
  }

  :deep(.el-table__cell) {
    padding: 8px 0;
  }

  /* 音频播放器样式优化 */
  audio {
    outline: none;
  }

  audio::-webkit-media-controls-panel {
    background-color: #f5f7fa;
  }
  </style>
  