
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
        <el-form-item label="用户ID" prop="user_id">
          <el-input v-model.number="searchInfo.user_id" placeholder="请输入用户ID" clearable />
        </el-form-item>
        <el-form-item label="用户昵称" prop="nickname">
          <el-input v-model="searchInfo.nickname" placeholder="请输入用户昵称" clearable />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="searchInfo.phone" placeholder="请输入手机号" clearable />
        </el-form-item>
        
        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增用户</el-button>
            <el-button type="primary" style="float: right;"  @click="rewardScore">发放奖励</el-button>
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="user_id"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        >
        <el-table-column type="selection" width="55" />
        
            <el-table-column align="left" label="用户ID" prop="user_id" width="120" sortable="custom" />

            <el-table-column align="left" label="用户昵称" prop="nickname" width="120" />

            <el-table-column align="left" label="名称拼音" prop="nickname_pinyin" width="120" />

            <el-table-column align="left" label="手机号" prop="phone" width="120" />

            <el-table-column align="left" label="头像" prop="avatar" width="120">
              <template #default="scope">
                <el-image
                  v-if="scope.row.avatar"
                  style="width: 50px; height: 50px"
                  :src="scope.row.avatar"
                  :preview-src-list="[scope.row.avatar]"
                  fit="cover"
                  :preview-teleported="true"
                />
                <span v-else class="no-image-resource">无图片资源</span>
              </template>
            </el-table-column>

            <el-table-column align="left" label="性别" prop="gender" width="80">
    <template #default="scope">
      {{ scope.row.gender === 1 ? '男' : scope.row.gender === 2 ? '女' : '未知' }}
    </template>
</el-table-column>
            <el-table-column align="left" label="状态" prop="status" width="80" sortable="custom">
    <template #default="scope">
      {{ scope.row.status === 1 ? '正常' : scope.row.status === 3 ? '禁用' : '注销' }}
    </template>
</el-table-column>
            <el-table-column align="left" label="背景图" prop="background_url" width="100" >
              <template #default="scope">
                <el-image
                  v-if="scope.row.background_url"
                  style="width: 50px; height: 50px"
                  :src="scope.row.background_url"
                  :preview-src-list="[scope.row.background_url]"
                  fit="contain"
                  :preview-teleported="true"
                />
                <span v-else class="no-image-resource">无图片资源</span>
              </template>
            </el-table-column>

            <el-table-column align="center" label="优质创作者" prop="is_premium_creator" width="120" sortable="custom">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.is_premium_creator"
                  @change="handlePremiumCreatorChange(scope.row)"
                  :loading="scope.row.premiumCreatorLoading"
                  active-text="是"
                  inactive-text="否"
                  size="small"
                />
              </template>
            </el-table-column>

            <el-table-column align="left" label="语音签名" prop="voice_signature_url" width="150">
              <template #default="scope">
                <div v-if="scope.row.voice_signature_url">
                  <audio
                    controls
                    :src="scope.row.voice_signature_url"
                    style="width: 100%; height: 28px;"
                  >
                    您的浏览器不支持音频播放
                  </audio>
                  <div style="font-size: 12px; color: #666; margin-top: 2px;">
                    {{ Math.round(scope.row.voice_duration / 1000) }}秒
                  </div>
                </div>
                <span v-else style="color: #999;">无音频</span>
              </template>
            </el-table-column>

            <el-table-column align="left" label="审核中语音签名" prop="review_voice_signature_url" width="150" sortable="custom">
              <template #default="scope">
                <div v-if="scope.row.review_voice_signatureUrl">
                  <audio
                    controls
                    :src="scope.row.review_voice_signatureUrl"
                    style="width: 100%; height: 28px;"
                  >
                    您的浏览器不支持音频播放
                  </audio>
                  <div style="font-size: 12px; color: #666; margin-top: 2px;">
                    {{ Math.round(scope.row.review_voice_duration / 1000) }}秒
                  </div>
                </div>
                <span v-else style="color: #999;">无音频</span>
              </template>
            </el-table-column>
            <el-table-column align="left" label="创建时间" prop="created_at" width="140" sortable="custom">
              <template #default="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>

            <el-table-column align="left" label="更新时间" prop="updated_at" width="140" >
              <template #default="scope">
                {{ formatDate(scope.row.updated_at) }}
              </template>
            </el-table-column>

        <el-table-column align="left" label="操作" fixed="right" min-width="320">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getScore(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看积分</el-button>
            <el-button  type="primary" link class="table-button" @click="updateUserFunc(scope.row)"><el-icon style="margin-right: 5px"><Edit /></el-icon>编辑</el-button>
            <el-button  type="warning" link class="table-button" @click="resetUserInfoFunc(scope.row)"><el-icon style="margin-right: 5px"><RefreshLeft /></el-icon>重置</el-button>
            <el-button  type="danger" link class="table-button" @click="banUserFunc(scope.row)" v-if="scope.row.status === 1"><el-icon style="margin-right: 5px"><Lock /></el-icon>封禁</el-button>
            <el-button  type="success" link class="table-button" @click="unbanUserFunc(scope.row)" v-if="scope.row.status === 3"><el-icon style="margin-right: 5px"><Unlock /></el-icon>解封</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[20, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            <el-form-item label="手机号:" prop="phone" v-if="type === 'create'">
    <el-input v-model="formData.phone" :clearable="true" placeholder="请输入手机号" />
</el-form-item>
            <el-form-item label="用户昵称:" prop="nickname">
    <el-input v-model="formData.nickname" :clearable="true" placeholder="请输入用户昵称（留空将自动生成）" />
</el-form-item>
            <el-form-item label="头像:" prop="avatar">
              <div style="display: flex; flex-direction: column; gap: 12px;">
                <div style="display: flex; gap: 12px; align-items: center;">
                  <el-input
                    v-model="formData.avatar"
                    placeholder="请输入头像URL或点击选择/上传"
                    style="flex: 1;"
                    clearable
                  />
                  <el-button type="primary" @click="openAvatarImageSelector">
                    选择图片
                  </el-button>
                  <el-upload
                    :auto-upload="false"
                    :show-file-list="false"
                    :on-change="handleAvatarImageUpload"
                    accept="image/*"
                  >
                    <el-button type="success" :loading="avatarImageUploading">
                      {{ avatarImageUploading ? '上传中...' : '上传图片' }}
                    </el-button>
                  </el-upload>
                  <el-button
                    v-if="formData.avatar"
                    type="danger"
                    @click="clearAvatarImage"
                  >
                    清除
                  </el-button>
                </div>
                <div v-if="formData.avatar" style="display: flex; align-items: center; gap: 12px;">
                  <el-image
                    :src="formData.avatar"
                    :preview-src-list="[formData.avatar]"
                    fit="cover"
                    style="width: 80px; height: 80px; border-radius: 50%; border: 1px solid #ddd; cursor: pointer;"
                    :preview-teleported="true"
                  />
                </div>
              </div>
            </el-form-item>
            <el-form-item label="性别:" prop="gender">
    <el-select v-model="formData.gender" placeholder="请选择性别">
      <el-option label="未知" :value="0"></el-option>
      <el-option label="男" :value="1"></el-option>
      <el-option label="女" :value="2"></el-option>
    </el-select>
</el-form-item>
            <el-form-item label="状态:" prop="status">
    <el-select v-model="formData.status" placeholder="请选择状态">
      <el-option label="正常" :value="1"></el-option>
      <el-option label="禁用" :value="3"></el-option>
    </el-select>
</el-form-item>
            <el-form-item label="背景图:" prop="background_url">
              <div style="display: flex; flex-direction: column; gap: 12px;">
                <div style="display: flex; gap: 12px; align-items: center;">
                  <el-input
                    v-model="formData.background_url"
                    placeholder="请输入背景图URL或点击选择/上传"
                    style="flex: 1;"
                    clearable
                  />
                  <el-button type="primary" @click="openBackgroundImageSelector">
                    选择图片
                  </el-button>
                  <el-upload
                    :auto-upload="false"
                    :show-file-list="false"
                    :on-change="handleBackgroundImageUpload"
                    accept="image/*"
                  >
                    <el-button type="success" :loading="backgroundImageUploading">
                      {{ backgroundImageUploading ? '上传中...' : '上传图片' }}
                    </el-button>
                  </el-upload>
                  <el-button
                    v-if="formData.background_url"
                    type="danger"
                    @click="clearBackgroundImage"
                  >
                    清除
                  </el-button>
                </div>
                <div v-if="formData.background_url" style="display: flex; align-items: center; gap: 12px;">
                  <el-image
                    :src="formData.background_url"
                    :preview-src-list="[formData.background_url]"
                    fit="cover"
                    style="width: 120px; height: 80px; border-radius: 4px; border: 1px solid #ddd; cursor: pointer;"
                    :preview-teleported="true"
                  />
                </div>
              </div>
            </el-form-item>

            <el-form-item label="是否优质创作者:" prop="is_premium_creator">
    <el-switch v-model="formData.is_premium_creator" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
</el-form-item>
            <el-form-item label="关联创作者:" prop="creation_user_id">
              <el-select
                v-model="formData.creation_user_id"
                placeholder="请选择关联的创作者用户"
                clearable
                style="width: 100%"
                @change="handleCreatorChange"
              >
                <el-option
                  v-for="creator in creatorUserList"
                  :key="creator.ID"
                  :label="`${creator.nickName} (${creator.userName})`"
                  :value="creator.ID"
                />
              </el-select>
            </el-form-item>
          </el-form>
    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="scoreShow" :show-close="true" :before-close="closeScoreShow" title="查看积分">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="用户ID">
    {{ scoreFrom.user_id }}
</el-descriptions-item>
                    <el-descriptions-item label="免费金币余额">
    {{ scoreFrom.free_balance/100 }}
</el-descriptions-item>
                    <el-descriptions-item label="有价值金币余额">
    {{ scoreFrom.valuable_balance/100 }}
</el-descriptions-item>
                    <el-descriptions-item label="免费总金币">
    {{ scoreFrom.total_free/100 }}
</el-descriptions-item>
                    <el-descriptions-item label="有价值总金币">
    {{ scoreFrom.total_valuable/100 }}
</el-descriptions-item>           
                    <el-descriptions-item label="创建时间">
    {{ scoreFrom?.created_at ? formatDate(scoreFrom.created_at) : '-' }}
</el-descriptions-item>  
            </el-descriptions>
        </el-drawer>

  <!--发放奖励窗口-->
  <el-dialog
      v-model="rewardDialogVisible"
      title="发放奖励"
      width="30%"
      :before-close="handleRewardDialogClose"
    >
      <el-form :model="rewardForm">
        <el-form-item label="用户ID" prop="user_id">
          <el-input 
            v-model.number="rewardForm.user_id" 
            placeholder="请输入用户ID" 
            @keyup.enter="queryUserInfo"
          />
        </el-form-item>
        <el-form-item label="奖励金币" prop="coins">
          <el-input  v-model.number="rewardForm.coins" placeholder="请输入奖励金币" />
        </el-form-item>
      </el-form>
      <el-form-item label="用户昵称" prop="userInfo.nickname">
          <span>{{ userInfo.nickname }}</span>
      </el-form-item>
      <el-form-item label="手机号" prop="userInfo.phone">
          <span>{{ userInfo.phone }}</span>
      </el-form-item>
      <el-form-item label="用户头像">
         <el-image
            v-if="userInfo.avatar"
            style="width: 50px; height: 50px; border-radius: 50%"
            :src="userInfo.avatar"
            :preview-src-list="[userInfo.avatar]"
            fit="cover"
            :preview-teleported="true"
         />
         <span v-else class="no-image-resource">无图片资源</span>
      </el-form-item>
      <el-form-item label="奖励类型" prop="reward_type">
        <el-select v-model="rewardForm.reward_type" placeholder="请选择奖励类型" @change="handleRewardTypeChange">
           <el-option :label="'系统奖励'" :value="6"/>
        </el-select>
      </el-form-item>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rewardDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReward" :loading="rewardLoading">
            确认发放
          </el-button>
        </span>
      </template>
    </el-dialog>

  <!-- 头像图片选择弹窗 -->
  <el-dialog v-model="avatarImageSelectorVisible" title="选择头像" width="80%" :before-close="closeAvatarImageSelector">
    <div class="avatar-image-selector">
      <div class="selector-toolbar" style="margin-bottom: 16px;">
        <el-input
          v-model="avatarImageSearch"
          placeholder="搜索图片..."
          style="width: 300px; margin-right: 12px;"
          clearable
          @input="searchAvatarImages"
        />
        <el-button type="primary" @click="refreshAvatarImages">刷新</el-button>
      </div>
      <div class="image-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 12px; max-height: 400px; overflow-y: auto;">
        <div
          v-for="image in filteredAvatarImages"
          :key="image.id || image.url"
          class="image-item"
          style="border: 2px solid transparent; border-radius: 8px; cursor: pointer; transition: all 0.2s;"
          :style="{ borderColor: selectedAvatarImage?.url === image.url ? '#409EFF' : 'transparent' }"
          @click="selectAvatarImage(image)"
        >
          <el-image
            :src="image.url"
            fit="cover"
            style="width: 100%; height: 100px; border-radius: 6px;"
            :preview-src-list="[image.url]"
            :preview-teleported="true"
          />
          <div style="padding: 8px; text-align: center; font-size: 12px; color: #666;">
            {{ image.name || '未命名' }}
          </div>
        </div>
      </div>
      <div v-if="filteredAvatarImages.length === 0" style="text-align: center; padding: 40px; color: #999;">
        暂无图片数据
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeAvatarImageSelector">取消</el-button>
        <el-button type="primary" @click="confirmAvatarImageSelection" :disabled="!selectedAvatarImage">
          确定选择
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 背景图选择弹窗 -->
  <el-dialog v-model="backgroundImageSelectorVisible" title="选择背景图" width="80%" :before-close="closeBackgroundImageSelector">
    <div class="background-image-selector">
      <div class="selector-toolbar" style="margin-bottom: 16px;">
        <el-input
          v-model="backgroundImageSearch"
          placeholder="搜索图片..."
          style="width: 300px; margin-right: 12px;"
          clearable
          @input="searchBackgroundImages"
        />
        <el-button type="primary" @click="refreshBackgroundImages">刷新</el-button>
      </div>
      <div class="image-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 12px; max-height: 400px; overflow-y: auto;">
        <div
          v-for="image in filteredBackgroundImages"
          :key="image.id || image.url"
          class="image-item"
          style="border: 2px solid transparent; border-radius: 8px; cursor: pointer; transition: all 0.2s;"
          :style="{ borderColor: selectedBackgroundImage?.url === image.url ? '#409EFF' : 'transparent' }"
          @click="selectBackgroundImage(image)"
        >
          <el-image
            :src="image.url"
            fit="cover"
            style="width: 100%; height: 100px; border-radius: 6px;"
            :preview-src-list="[image.url]"
            :preview-teleported="true"
          />
          <div style="padding: 8px; text-align: center; font-size: 12px; color: #666;">
            {{ image.name || '未命名' }}
          </div>
        </div>
      </div>
      <div v-if="filteredBackgroundImages.length === 0" style="text-align: center; padding: 40px; color: #999;">
        暂无图片数据
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeBackgroundImageSelector">取消</el-button>
        <el-button type="primary" @click="confirmBackgroundImageSelection" :disabled="!selectedBackgroundImage">
          确定选择
        </el-button>
      </span>
    </template>
  </el-dialog>

  </div>
</template>

<script setup>
import {
  createUser,
  deleteUser,
  deleteUserByIds,
  updateUser,
  findUser,
  getUserList,
  getUserScore,
  rewardUserScore,
  resetUserInfo,
  banUser,
  unbanUser,
  setPremiumCreator
} from '@/api/users/user'
import { getCreatorUserList } from '@/api/user'

// 全量引入格式化工具 请按需保留
import { formatDate } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { InfoFilled, RefreshLeft, Lock, Unlock, Edit } from '@element-plus/icons-vue'
import { ref, reactive } from 'vue'
import { useAppStore } from "@/pinia"
import { uploadToOSS, checkFileSize } from '@/utils/oss'
import { getFileList } from '@/api/fileUploadAndDownload'




defineOptions({
    name: 'User'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 创作者用户列表
const creatorUserList = ref([])

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(true)

// 头像图片选择相关状态
const avatarImageSelectorVisible = ref(false)
const avatarImageSearch = ref('')
const avatarImages = ref([])
const filteredAvatarImages = ref([])
const selectedAvatarImage = ref(null)
const avatarImageUploading = ref(false)

// 背景图选择相关状态
const backgroundImageSelectorVisible = ref(false)
const backgroundImageSearch = ref('')
const backgroundImages = ref([])
const filteredBackgroundImages = ref([])
const selectedBackgroundImage = ref(null)
const backgroundImageUploading = ref(false)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            user_id: undefined,
            nickname: '',
            nickname_pinyin: '',
            phone: '',
            avatar: '',
            gender: 0,
            status: 1,
            background_url: '',
            is_premium_creator: false,
            creation_user_id: undefined,
            created_at: undefined,
            updated_at: undefined,
        })



// 验证规则
const rule = reactive({
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  nickname: [
    { required: false, message: '请输入用户昵称', trigger: 'blur' }
  ],
  gender: [
    { required: false, message: '请选择性别', trigger: 'change' }
  ],
  status: [
    { required: false, message: '请选择状态', trigger: 'change' }
  ]
})

const searchRule = reactive({
  CreatedAt: [
    { validator: (rule, value, callback) => {
      if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
        callback(new Error('请填写结束日期'))
      } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
        callback(new Error('请填写开始日期'))
      } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
        callback(new Error('开始日期应当早于结束日期'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(20)
const tableData = ref([])
const searchInfo = ref({})

// 排序相关
const sortInfo = ref({
  sort: '',
  order: ''
})
// 重置
const onReset = () => {
  searchInfo.value = {}
  sortInfo.value = { sort: '', order: '' }
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    if (searchInfo.value.gender === ""){
        searchInfo.value.gender=null
    }
    if (searchInfo.value.status === ""){
        searchInfo.value.status=null
    }
    if (searchInfo.value.is_premium_creator === ""){
        searchInfo.value.is_premium_creator=null
    }
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 处理排序变化
const handleSortChange = ({ prop, order }) => {
  if (order === null) {
    sortInfo.value = { sort: '', order: '' }
  } else {
    sortInfo.value = {
      sort: prop,
      order: order === 'ascending' ? 'asc' : 'desc'
    }
  }
  page.value = 1
  getTableData()
}

// 查询
const getTableData = async() => {
  const params = {
    page: page.value,
    pageSize: pageSize.value,
    ...searchInfo.value,
    ...sortInfo.value
  }
  const table = await getUserList(params)
  if (table.code === 0) {
    // 为每行数据添加loading状态
    tableData.value = table.data.list.map(item => ({
      ...item,
      premiumCreatorLoading: false
    }))
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteUserFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const user_ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          user_ids.push(item.user_id)
        })
      const res = await deleteUserByIds({ user_ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === user_ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateUserFunc = async(row) => {
    const res = await findUser({ user_id: row.user_id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        await getCreatorUsers() // 获取创作者用户列表
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteUserFunc = async (row) => {
    const res = await deleteUser({ user_id: row.user_id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 获取创作者用户列表
const getCreatorUsers = async () => {
  try {
    const res = await getCreatorUserList()
    if (res.code === 0) {
      creatorUserList.value = res.data || []
      console.log('获取创作者用户列表成功:', creatorUserList.value.length, '个用户')
    } else {
      console.error('获取创作者用户列表失败:', res.msg)
      ElMessage.error('获取创作者用户列表失败: ' + res.msg)
      creatorUserList.value = []
    }
  } catch (error) {
    console.error('获取创作者用户列表异常:', error)
    ElMessage.error('获取创作者用户列表异常，请稍后重试')
    creatorUserList.value = []
  }
}

// 处理创作者选择变化
const handleCreatorChange = (value) => {
  console.log('选择创作者ID:', value)
}

// 打开弹窗
const openDialog = async () => {
    type.value = 'create'
    await getCreatorUsers() // 获取创作者用户列表
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        user_id: undefined,
        nickname: '',
        nickname_pinyin: '',
        phone: '',
        avatar: '',
        gender: 0,
        status: 1,
        background_url: '',
        is_premium_creator: false,
        creation_user_id: undefined,
        created_at: undefined,
        updated_at: undefined,
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createUser(formData.value)
                  break
                case 'update':
                  res = await updateUser(formData.value)
                  break
                default:
                  res = await createUser(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const scoreFrom = ref({})

// 查看详情控制标记
const scoreShow = ref(false)


// 打开详情弹窗
const openScoreShow = () => {
  scoreShow.value = true
}


// 打开详情
const getScore = async (row) => {
  // 打开弹窗
  const res = await getUserScore({ user_id: row.user_id })
  if (res.code === 0) {
    scoreFrom.value = res.data
    openScoreShow()
  }
}


// 关闭详情弹窗
const closeScoreShow = () => {
  scoreShow.value = false
  scoreFrom.value = {}
}


// 查询用户信息
const userInfo = ref({
  nickname: '',
  phone: '',
  avatar: ''
})

const queryUserInfo = async () => {
  if (!rewardForm.value.user_id) {
    ElMessage.warning('请输入用户ID')
    return
  }

  try {
    // Replace 'getUserInfo' with your actual API endpoint
    const res = await findUser({ user_id: rewardForm.value.user_id })
    if (res.code === 0) {
      userInfo.value = {
        nickname: res.data.nickname || '',
        phone: res.data.phone || '',
        avatar: res.data.avatar || ''
      }
    } else {
      ElMessage.error(res.msg || '查询用户信息失败')
      userInfo.value = {
        nickname: '',
        phone: '',
        avatar: ''
      }
    }
  } catch (error) {
    ElMessage.error('查询用户信息失败，请重试')
    userInfo.value = {
      nickname: '',
      phone: '',
      avatar: ''
    }
  }
}

// 发放奖励相关
const rewardDialogVisible = ref(false)
const rewardForm = ref({
  user_id: undefined,
  reward_type: 6,
  reward_name: '系统奖励'
})
const rewardLoading = ref(false)

const handleRewardTypeChange = (value) => {
  switch (value) {
    case 6:
      rewardForm.value.reward_name = '系统奖励'
      break
    default:
       rewardForm.value.reward_name = '系统奖励'
       break
  }
}


const rewardScore = () => {
  rewardForm.value = {
    user_id: undefined,
    coins: undefined,
    reward_type: 6,  // 默认系统奖励
    reward_name: '系统奖励'
  }
  userInfo.value = {
    nickname: '',
    phone: '',
    avatar: ''
  }
  rewardDialogVisible.value = true
}

const handleRewardDialogClose = () => {
  rewardDialogVisible.value = false
  rewardForm.value = {
    user_id: undefined,
    coins: undefined,
    reward_type: 6,
    reward_name: '系统奖励'
  }
  userInfo.value = {
    nickname: '',
    phone: '',
    avatar: ''
  }
}

const submitReward = async () => {
  if (!rewardForm.value.user_id) {
    ElMessage.warning('请输入用户ID')
    return
  }
  if (!rewardForm.value.reward_type) {
    ElMessage.warning('请选择奖励类型')
    return
  }
  if (!rewardForm.value.coins) {
    ElMessage.warning('请输入发放金币')
    return
  }
  
  rewardLoading.value = true
  try {
    // Replace 'rewardUserScore' with your actual API endpoint
    const res = await rewardUserScore({
       user_id: rewardForm.value.user_id, 
       reward_type: rewardForm.value.reward_type,
       reward_name: rewardForm.value.reward_name,
       coins: rewardForm.value.coins
    })
    if (res.code === 0) {
      ElMessage.success('奖励发放成功')
      rewardDialogVisible.value = false
    } else {
      ElMessage.error(res.msg || '奖励发放失败')
    }
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  } finally {
    rewardLoading.value = false
  }
}

// 重置用户信息
const resetUserInfoFunc = async (row) => {
  ElMessageBox.confirm('确定要重置该用户的信息吗？重置后用户的昵称、头像、背景图、语音签名将恢复为默认值。', '重置确认', {
    confirmButtonText: '确定重置',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await resetUserInfo({ user_id: row.user_id })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '用户信息重置成功'
        })
        getTableData()
      } else {
        ElMessage.error(res.msg || '重置失败')
      }
    } catch (error) {
      ElMessage.error('操作失败，请重试')
    }
  })
}

// 封禁用户
const banUserFunc = async (row) => {
  ElMessageBox.confirm('确定要封禁该用户吗？封禁后用户将无法正常使用应用。', '封禁确认', {
    confirmButtonText: '确定封禁',
    cancelButtonText: '取消',
    type: 'error'
  }).then(async () => {
    try {
      const res = await banUser({ user_id: row.user_id })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '用户封禁成功'
        })
        getTableData()
      } else {
        ElMessage.error(res.msg || '封禁失败')
      }
    } catch (error) {
      ElMessage.error('操作失败，请重试')
    }
  })
}

// 解封用户
const unbanUserFunc = async (row) => {
  ElMessageBox.confirm('确定要解封该用户吗？解封后用户将恢复正常使用权限。', '解封确认', {
    confirmButtonText: '确定解封',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await unbanUser({ user_id: row.user_id })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '用户解封成功'
        })
        getTableData()
      } else {
        ElMessage.error(res.msg || '解封失败')
      }
    } catch (error) {
      ElMessage.error('操作失败，请重试')
    }
  })
}

// 处理优质创作者开关变化
const handlePremiumCreatorChange = async (row) => {
  const action = row.is_premium_creator ? '设置为' : '取消'

  ElMessageBox.confirm(`确定要${action}优质创作者吗？`, '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    // 设置loading状态
    row.premiumCreatorLoading = true

    try {
      const res = await setPremiumCreator({
        user_id: row.user_id,
        is_premium_creator: row.is_premium_creator
      })

      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: `${action}优质创作者成功`
        })
        // 刷新表格数据以确保状态同步
        getTableData()
      } else {
        // 操作失败，恢复开关状态
        row.is_premium_creator = !row.is_premium_creator
        ElMessage.error(res.msg || `${action}优质创作者失败`)
      }
    } catch (error) {
      // 操作失败，恢复开关状态
      row.is_premium_creator = !row.is_premium_creator
      ElMessage.error('操作失败，请重试')
    } finally {
      row.premiumCreatorLoading = false
    }
  }).catch(() => {
    // 用户取消操作，恢复开关状态
    row.is_premium_creator = !row.is_premium_creator
  })
}

// ============== 头像图片选择相关方法 ==============
// 打开头像图片选择器
const openAvatarImageSelector = async () => {
  await loadAvatarImages()
  avatarImageSelectorVisible.value = true
}

// 关闭头像图片选择器
const closeAvatarImageSelector = () => {
  avatarImageSelectorVisible.value = false
  selectedAvatarImage.value = null
  avatarImageSearch.value = ''
}

// 加载头像图片列表
const loadAvatarImages = async () => {
  try {
    const res = await getFileList({
      page: 1,
      pageSize: 100,
      keyword: '',
      classId: 0
    })
    if (res.code === 0) {
      // 过滤出图片文件
      avatarImages.value = (res.data.list || []).filter(file =>
        file.url && (
          file.url.toLowerCase().includes('.jpg') ||
          file.url.toLowerCase().includes('.jpeg') ||
          file.url.toLowerCase().includes('.png') ||
          file.url.toLowerCase().includes('.gif') ||
          file.url.toLowerCase().includes('.webp')
        )
      )
      filteredAvatarImages.value = avatarImages.value
    }
  } catch (error) {
    console.error('加载头像图片列表失败:', error)
    ElMessage.error('加载图片列表失败')
  }
}

// 搜索头像图片
const searchAvatarImages = () => {
  if (!avatarImageSearch.value.trim()) {
    filteredAvatarImages.value = avatarImages.value
  } else {
    const keyword = avatarImageSearch.value.toLowerCase()
    filteredAvatarImages.value = avatarImages.value.filter(image =>
      (image.name && image.name.toLowerCase().includes(keyword)) ||
      (image.url && image.url.toLowerCase().includes(keyword))
    )
  }
}

// 刷新头像图片列表
const refreshAvatarImages = () => {
  loadAvatarImages()
}

// 选择头像图片
const selectAvatarImage = (image) => {
  selectedAvatarImage.value = image
}

// 确认头像图片选择
const confirmAvatarImageSelection = () => {
  if (selectedAvatarImage.value) {
    formData.value.avatar = selectedAvatarImage.value.url
    ElMessage.success('头像选择成功')
    closeAvatarImageSelector()
  }
}

// 处理头像图片上传
const handleAvatarImageUpload = async (file) => {
  try {
    avatarImageUploading.value = true

    // 检查文件大小
    checkFileSize(file.raw, 10) // 最大10MB

    // 上传到OSS
    const result = await uploadToOSS(file.raw, 'image', 'avatars')

    // 更新表单数据
    formData.value.avatar = result.url

    ElMessage.success('头像上传成功')

  } catch (error) {
    ElMessage.error(error.message || '上传失败')
  } finally {
    avatarImageUploading.value = false
  }
}

// 清除头像图片
const clearAvatarImage = () => {
  formData.value.avatar = ''
}

// ============== 背景图选择相关方法 ==============
// 打开背景图选择器
const openBackgroundImageSelector = async () => {
  await loadBackgroundImages()
  backgroundImageSelectorVisible.value = true
}

// 关闭背景图选择器
const closeBackgroundImageSelector = () => {
  backgroundImageSelectorVisible.value = false
  selectedBackgroundImage.value = null
  backgroundImageSearch.value = ''
}

// 加载背景图列表
const loadBackgroundImages = async () => {
  try {
    const res = await getFileList({
      page: 1,
      pageSize: 100,
      keyword: '',
      classId: 0
    })
    if (res.code === 0) {
      // 过滤出图片文件
      backgroundImages.value = (res.data.list || []).filter(file =>
        file.url && (
          file.url.toLowerCase().includes('.jpg') ||
          file.url.toLowerCase().includes('.jpeg') ||
          file.url.toLowerCase().includes('.png') ||
          file.url.toLowerCase().includes('.gif') ||
          file.url.toLowerCase().includes('.webp')
        )
      )
      filteredBackgroundImages.value = backgroundImages.value
    }
  } catch (error) {
    console.error('加载背景图列表失败:', error)
    ElMessage.error('加载图片列表失败')
  }
}

// 搜索背景图
const searchBackgroundImages = () => {
  if (!backgroundImageSearch.value.trim()) {
    filteredBackgroundImages.value = backgroundImages.value
  } else {
    const keyword = backgroundImageSearch.value.toLowerCase()
    filteredBackgroundImages.value = backgroundImages.value.filter(image =>
      (image.name && image.name.toLowerCase().includes(keyword)) ||
      (image.url && image.url.toLowerCase().includes(keyword))
    )
  }
}

// 刷新背景图列表
const refreshBackgroundImages = () => {
  loadBackgroundImages()
}

// 选择背景图
const selectBackgroundImage = (image) => {
  selectedBackgroundImage.value = image
}

// 确认背景图选择
const confirmBackgroundImageSelection = () => {
  if (selectedBackgroundImage.value) {
    formData.value.background_url = selectedBackgroundImage.value.url
    ElMessage.success('背景图选择成功')
    closeBackgroundImageSelector()
  }
}

// 处理背景图上传
const handleBackgroundImageUpload = async (file) => {
  try {
    backgroundImageUploading.value = true

    // 检查文件大小
    checkFileSize(file.raw, 10) // 最大10MB

    // 上传到OSS
    const result = await uploadToOSS(file.raw, 'image', 'backgrounds')

    // 更新表单数据
    formData.value.background_url = result.url

    ElMessage.success('背景图上传成功')

  } catch (error) {
    ElMessage.error(error.message || '上传失败')
  } finally {
    backgroundImageUploading.value = false
  }
}

// 清除背景图
const clearBackgroundImage = () => {
  formData.value.background_url = ''
}


</script>

<style scoped>
/* 无图片资源样式 */
.no-image-resource {
  color: #C0C4CC;
  font-style: italic;
  font-size: 12px;
}
</style>
