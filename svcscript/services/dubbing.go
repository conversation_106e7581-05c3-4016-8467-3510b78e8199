package services

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"new-gitlab.xunlei.cn/vcproject/backends/svcscript/mq/event/pusher"

	"new-gitlab.xunlei.cn/vcproject/backends/svcscript/cache"

	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/basemsgtransfer"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcchat"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/vcxxjob"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
	"new-gitlab.xunlei.cn/vcproject/backends/svcscript/model"

	"golang.org/x/sync/errgroup"
)

// DubbingServiceInterface 配音服务接口
type DubbingServiceInterface interface {
	GetLineDubbings(ctx context.Context, req *svcscript.GetLineDubbingsReq) (*svcscript.GetLineDubbingsResp, error)
	GetLineDubbingsSimple(ctx context.Context, req *svcscript.GetLineDubbingsSimpleReq) (*svcscript.GetLineDubbingsSimpleResp, error)
	GetScriptFirstDubbing(ctx context.Context, req *svcscript.GetScriptFirstDubbingReq) (*svcscript.GetScriptFirstDubbingResp, error)
	BatchGetScriptFirstDubbing(ctx context.Context, req *svcscript.BatchGetScriptFirstDubbingReq) (*svcscript.BatchGetScriptFirstDubbingResp, error)
	CreateDubbing(ctx context.Context, req *svcscript.CreateDubbingReq) (int64, error)
	DeleteDubbingRecord(ctx context.Context, req *svcscript.DeleteDubbingRecordReq) (*svcscript.DeleteDubbingRecordResp, error)
	DeleteDubbing(ctx context.Context, req *svcscript.DeleteDubbingReq) error
	SetDubbingTop(ctx context.Context, req *svcscript.SetDubbingTopReq) error
	NotifyDubbing(ctx context.Context, dubbing *model.Dubbing, dubbingId ...int64)
	//NotifyDubbingOnReviewReject(ctx context.Context, dubbing *model.Dubbing, dubbingId ...int64)

	// 管理后台方法
	AdminDeleteDubbing(ctx context.Context, dubbingID int64) error
	AdminAuditDubbing(ctx context.Context, dubbingID int64, reviewStatus svcscript.ReviewStatus, rejectReason string) error
	ReDubbingByAI(ctx context.Context, req *svcscript.ReDubbingByAIReq) (*svcscript.ReDubbingByAIResp, error)
	ManualUploadDubbing(ctx context.Context, req *svcscript.ManualUploadDubbingReq) (int64, error)
}

// DubbingService 配音服务
type DubbingService struct {
	baseModel              model.BaseModelInterface
	dubbingModel           model.DubbingModelInterface
	dubbingRecordModel     model.DubbingRecordModelInterface
	lineModel              model.LineModelInterface
	characterModel         model.CharacterModelInterface
	characterAssetModel    model.CharacterAssetModelInterface
	likeModel              model.LikeModelInterface
	commentModel           model.CommentModelInterface
	dubbingCompletionModel model.DubbingCompletionModelInterface
	scriptModel            model.ScriptModelInterface
	eventPusher            pusher.EventScriptPushInterface
	characterSvc           CharacterServiceInterface
	limiter                cache.RateLimiterInterface
}

// NewDubbingService 创建配音服务实例
func NewDubbingService(
	baseModel model.BaseModelInterface,
	dubbingModel model.DubbingModelInterface,
	dubbingRecordModel model.DubbingRecordModelInterface,
	lineModel model.LineModelInterface,
	characterModel model.CharacterModelInterface,
	characterAssetModel model.CharacterAssetModelInterface,
	likeModel model.LikeModelInterface,
	commentModel model.CommentModelInterface,
	dubbingCompletionModel model.DubbingCompletionModelInterface,
	scriptModel model.ScriptModelInterface,
	eventPusher pusher.EventScriptPushInterface,
	characterSvc CharacterServiceInterface,
	limiter cache.RateLimiterInterface,
) DubbingServiceInterface {
	return &DubbingService{
		baseModel:              baseModel,
		dubbingModel:           dubbingModel,
		dubbingRecordModel:     dubbingRecordModel,
		lineModel:              lineModel,
		characterModel:         characterModel,
		characterAssetModel:    characterAssetModel,
		likeModel:              likeModel,
		commentModel:           commentModel,
		dubbingCompletionModel: dubbingCompletionModel,
		scriptModel:            scriptModel,
		eventPusher:            eventPusher,
		characterSvc:           characterSvc,
		limiter:                limiter,
	}
}

// GetLineDubbings 获取台词的完整配音列表 (按照新的5组排序逻辑)
func (s *DubbingService) GetLineDubbings(ctx context.Context, req *svcscript.GetLineDubbingsReq) (*svcscript.GetLineDubbingsResp, error) {
	if req.Page == 0 {
		req.Page = consts.DefaultPage
	}
	if req.PageSize == 0 {
		req.PageSize = consts.DefaultPageSize
	}

	// 获取台词的所有配音数据（不分页）
	allDubbings, err := s.dubbingModel.GetAllLineDubbings(ctx, req.UserId, req.LineId)
	if err != nil {
		return nil, err
	}

	if len(allDubbings) == 0 {
		return &svcscript.GetLineDubbingsResp{
			Base: errcode.ErrOK.ToSvcBaseResp(),
			Data: &svcscript.GetLineDubbingsRespData{
				Dubbings: []*svcscript.Dubbing{},
				Total:    0,
			},
		}, nil
	}

	// 应用完整列表排序逻辑（不限制数量）
	sortedDubbings := s.applySortingLogicForComplete(allDubbings, req.UserId, []int64{})

	total := int64(len(sortedDubbings))
	offset := int((req.Page - 1) * req.PageSize)
	limit := int(req.PageSize)

	var pagedDubbings []*model.Dubbing
	if offset < len(sortedDubbings) {
		end := offset + limit
		if end > len(sortedDubbings) {
			end = len(sortedDubbings)
		}
		pagedDubbings = sortedDubbings[offset:end]
	}

	if len(pagedDubbings) == 0 {
		return &svcscript.GetLineDubbingsResp{
			Base: errcode.ErrOK.ToSvcBaseResp(),
			Data: &svcscript.GetLineDubbingsRespData{
				Dubbings: []*svcscript.Dubbing{},
				Total:    total,
			},
		}, nil
	}

	// 处理配音列表，获取关联用户和角色信息
	pbDubbings, isDubbing, err := s.processDubbings(ctx, pagedDubbings, req.UserId)
	if err != nil {
		return nil, err
	}

	return &svcscript.GetLineDubbingsResp{
		Base: errcode.ErrOK.ToSvcBaseResp(),
		Data: &svcscript.GetLineDubbingsRespData{
			Dubbings:  pbDubbings,
			Total:     total,
			IsDubbing: isDubbing,
		},
	}, nil
}

// GetLineDubbingsSimple 获取台词的简略配音列表（优化版本）
func (s *DubbingService) GetLineDubbingsSimple(ctx context.Context, req *svcscript.GetLineDubbingsSimpleReq) (*svcscript.GetLineDubbingsSimpleResp, error) {
	// 获取剧本的所有台词
	lines, err := s.lineModel.GetLinesByScriptID(ctx, req.ScriptId)
	if err != nil {
		logger.Errorf("GetLinesByScriptID error: %v, scriptID: %d", err, req.ScriptId)
		return nil, err
	}

	if len(lines) == 0 {
		return &svcscript.GetLineDubbingsSimpleResp{
			Base: errcode.ErrOK.ToSvcBaseResp(),
			Data: &svcscript.GetLineDubbingsSimpleRespData{
				Dubbings: make(map[int64]*svcscript.DubbingList),
			},
		}, nil
	}

	// 使用优化后的批量处理方法
	return s.getLineDubbingsSimpleOptimized(ctx, req, lines)
}

// getLineDubbingsSimpleOptimized 优化后的简略配音列表获取方法，减少重复查询
// 应用默认排序（[批次置顶] > 用户配音 > 置顶配音 > 已完成角色配音 > 其他配音）
func (s *DubbingService) getLineDubbingsSimpleOptimized(ctx context.Context, req *svcscript.GetLineDubbingsSimpleReq, lines []*model.Line) (*svcscript.GetLineDubbingsSimpleResp, error) {
	// 1. 获取配音记录的台词配音映射（如果指定了批次ID）
	var recordLineDubbingMap map[int64][]int64
	if req.DubbingRecordId > 0 {
		var err error
		recordLineDubbingMap, err = s.dubbingRecordModel.GetLineDubbingMappingsByRecordID(ctx, req.DubbingRecordId)
		if err != nil {
			logger.Warnf("获取配音记录映射失败: %v, recordID: %d", err, req.DubbingRecordId)
			recordLineDubbingMap = make(map[int64][]int64)
		}
	}

	// 2. 批量获取所有台词的配音数据
	lineIDs := make([]int64, len(lines))
	for i, line := range lines {
		lineIDs[i] = line.ID
	}

	// 批量获取配音数据
	allDubbingsMap, err := s.batchGetLineDubbingsForSimple(ctx, lineIDs, req.UserId, req.ScriptId, recordLineDubbingMap)
	if err != nil {
		logger.Errorf("batchGetLineDubbingsForSimple error: %v", err)
		return nil, err
	}

	// 3. 收集所有需要的ID，进行批量查询
	allUserIDs, allCharacterIDs, allAssetIDs, allDubbingIDs := s.collectAllIDsFromDubbingsMap(allDubbingsMap)

	// 4. 批量获取所有关联数据
	batchData, err := s.batchGetAllRelatedData(ctx, req.UserId, req.ScriptId, allUserIDs, allCharacterIDs, allAssetIDs, allDubbingIDs)
	if err != nil {
		logger.Errorf("batchGetAllRelatedData error: %v", err)
		return nil, err
	}

	// 5. 组装结果
	dubbingsMap := make(map[int64]*svcscript.DubbingList)
	for _, line := range lines {
		dubbings, exists := allDubbingsMap[line.ID]
		if !exists || len(dubbings) == 0 {
			continue
		}

		// 批次置顶逻辑已经在 applySortingLogicForSimple 中处理，这里直接组装数据
		// 使用预加载的数据组装配音信息
		pbDubbings, isDubbing := s.assembleDubbingsWithBatchData(dubbings, req.UserId, batchData)
		if len(pbDubbings) > 0 {
			dubbingsMap[line.ID] = &svcscript.DubbingList{
				Dubbings:  pbDubbings,
				IsDubbing: isDubbing,
			}
		}
	}

	return &svcscript.GetLineDubbingsSimpleResp{
		Base: errcode.ErrOK.ToSvcBaseResp(),
		Data: &svcscript.GetLineDubbingsSimpleRespData{
			Dubbings: dubbingsMap,
		},
	}, nil
}

// GetScriptFirstDubbing 获取剧本所有台词的第一条配音，排序按GetLineDubbingsSimple逻辑（优化版本）
func (s *DubbingService) GetScriptFirstDubbing(ctx context.Context, req *svcscript.GetScriptFirstDubbingReq) (*svcscript.GetScriptFirstDubbingResp, error) {
	// 获取剧本的所有台词
	lines, err := s.lineModel.GetLinesByScriptID(ctx, req.ScriptId)
	if err != nil {
		logger.Errorf("GetLinesByScriptID error: %v, scriptID: %d", err, req.ScriptId)
		return nil, err
	}

	if len(lines) == 0 {
		return &svcscript.GetScriptFirstDubbingResp{
			Base: errcode.ErrOK.ToSvcBaseResp(),
			Data: &svcscript.GetScriptFirstDubbingData{
				Dubbings: make(map[int64]*svcscript.Dubbing),
			},
		}, nil
	}

	// 使用优化后的批量处理方法
	return s.getScriptFirstDubbingOptimized(ctx, req, lines)
}

// getScriptFirstDubbingOptimized 优化后的剧本首条配音获取方法
func (s *DubbingService) getScriptFirstDubbingOptimized(ctx context.Context, req *svcscript.GetScriptFirstDubbingReq, lines []*model.Line) (*svcscript.GetScriptFirstDubbingResp, error) {
	// 1. 批量获取所有台词的配音数据（只取第一条）
	lineIDs := make([]int64, len(lines))
	for i, line := range lines {
		lineIDs[i] = line.ID
	}

	// 批量获取配音数据（这里没有批次配音，传入nil）
	allDubbingsMap, err := s.batchGetLineDubbingsForSimple(ctx, lineIDs, req.UserId, req.ScriptId, nil)
	if err != nil {
		logger.Errorf("batchGetLineDubbingsForSimple error: %v", err)
		return nil, err
	}

	// 2. 收集所有需要的ID，进行批量查询
	allUserIDs, allCharacterIDs, allAssetIDs, allDubbingIDs := s.collectAllIDsFromDubbingsMap(allDubbingsMap)

	// 3. 批量获取所有关联数据
	batchData, err := s.batchGetAllRelatedData(ctx, req.UserId, req.ScriptId, allUserIDs, allCharacterIDs, allAssetIDs, allDubbingIDs)
	if err != nil {
		logger.Errorf("batchGetAllRelatedData error: %v", err)
		return nil, err
	}

	// 4. 组装结果（只取每个台词的第一条配音）
	dubbingsMap := make(map[int64]*svcscript.Dubbing)
	for _, line := range lines {
		dubbings, exists := allDubbingsMap[line.ID]
		if !exists || len(dubbings) == 0 {
			continue
		}

		// 只取第一条配音
		firstDubbing := dubbings[0]

		// 使用预加载的数据组装配音信息
		pbDubbings, _ := s.assembleDubbingsWithBatchData([]*model.Dubbing{firstDubbing}, req.UserId, batchData)
		if len(pbDubbings) > 0 {
			dubbingsMap[line.ID] = pbDubbings[0]
		}
	}

	return &svcscript.GetScriptFirstDubbingResp{
		Base: errcode.ErrOK.ToSvcBaseResp(),
		Data: &svcscript.GetScriptFirstDubbingData{
			Dubbings: dubbingsMap,
		},
	}, nil
}

// BatchGetScriptFirstDubbing 批量获取剧本所有台词的第一条配音（优化版本）
func (s *DubbingService) BatchGetScriptFirstDubbing(ctx context.Context, req *svcscript.BatchGetScriptFirstDubbingReq) (*svcscript.BatchGetScriptFirstDubbingResp, error) {
	if len(req.ScriptIds) == 0 {
		return &svcscript.BatchGetScriptFirstDubbingResp{
			Base: errcode.ErrOK.ToSvcBaseResp(),
			Data: &svcscript.BatchGetScriptFirstDubbingData{
				Scripts: make(map[int64]*svcscript.GetScriptFirstDubbingData),
			},
		}, nil
	}

	// 使用优化后的批量处理方法
	return s.batchGetScriptFirstDubbingOptimized(ctx, req)
}

// batchGetScriptFirstDubbingOptimized 优化后的批量剧本首条配音获取方法
func (s *DubbingService) batchGetScriptFirstDubbingOptimized(ctx context.Context, req *svcscript.BatchGetScriptFirstDubbingReq) (*svcscript.BatchGetScriptFirstDubbingResp, error) {
	// 1. 批量获取所有剧本的台词
	allLinesMap, err := s.batchGetLinesByScriptIDs(ctx, req.ScriptIds)
	if err != nil {
		logger.Errorf("batchGetLinesByScriptIDs error: %v", err)
		return nil, err
	}

	// 2. 收集所有台词ID
	var allLineIDs []int64
	for _, lines := range allLinesMap {
		for _, line := range lines {
			allLineIDs = append(allLineIDs, line.ID)
		}
	}

	if len(allLineIDs) == 0 {
		return &svcscript.BatchGetScriptFirstDubbingResp{
			Base: errcode.ErrOK.ToSvcBaseResp(),
			Data: &svcscript.BatchGetScriptFirstDubbingData{
				Scripts: make(map[int64]*svcscript.GetScriptFirstDubbingData),
			},
		}, nil
	}

	// 3. 批量获取所有台词的配音数据
	allDubbingsMap, err := s.batchGetLineDubbingsForAllScripts(ctx, allLineIDs, req.UserId, req.ScriptIds)
	if err != nil {
		logger.Errorf("batchGetLineDubbingsForAllScripts error: %v", err)
		return nil, err
	}

	// 4. 收集所有需要的ID，进行批量查询
	allUserIDs, allCharacterIDs, allAssetIDs, allDubbingIDs := s.collectAllIDsFromDubbingsMap(allDubbingsMap)

	// 5. 批量获取所有关联数据（这里需要处理多个剧本的情况）
	batchData, err := s.batchGetAllRelatedDataForMultipleScripts(ctx, req.UserId, req.ScriptIds, allUserIDs, allCharacterIDs, allAssetIDs, allDubbingIDs)
	if err != nil {
		logger.Errorf("batchGetAllRelatedDataForMultipleScripts error: %v", err)
		return nil, err
	}

	// 6. 按剧本组装结果
	scriptsMap := make(map[int64]*svcscript.GetScriptFirstDubbingData)
	for _, scriptID := range req.ScriptIds {
		lines, exists := allLinesMap[scriptID]
		if !exists {
			scriptsMap[scriptID] = &svcscript.GetScriptFirstDubbingData{
				Dubbings: make(map[int64]*svcscript.Dubbing),
			}
			continue
		}

		dubbingsMap := make(map[int64]*svcscript.Dubbing)
		for _, line := range lines {
			dubbings, exists := allDubbingsMap[line.ID]
			if !exists || len(dubbings) == 0 {
				continue
			}

			// 只取第一条配音
			firstDubbing := dubbings[0]

			// 使用预加载的数据组装配音信息
			pbDubbings, _ := s.assembleDubbingsWithBatchData([]*model.Dubbing{firstDubbing}, req.UserId, batchData)
			if len(pbDubbings) > 0 {
				dubbingsMap[line.ID] = pbDubbings[0]
			}
		}

		scriptsMap[scriptID] = &svcscript.GetScriptFirstDubbingData{
			Dubbings: dubbingsMap,
		}
	}

	return &svcscript.BatchGetScriptFirstDubbingResp{
		Base: errcode.ErrOK.ToSvcBaseResp(),
		Data: &svcscript.BatchGetScriptFirstDubbingData{
			Scripts: scriptsMap,
		},
	}, nil
}

// processDubbings 简化版处理配音方法，更健壮地处理错误
func (s *DubbingService) processDubbings(ctx context.Context, dubbings []*model.Dubbing, userID int64) ([]*svcscript.Dubbing, bool, error) {
	if len(dubbings) == 0 {
		return []*svcscript.Dubbing{}, false, nil
	}

	// 收集需要的ID列表
	userIDs := make([]int64, 0, len(dubbings))
	characterIDs := make([]int64, 0, len(dubbings))
	assetIDs := make([]int64, 0, len(dubbings))
	dubbingIDs := make([]int64, 0, len(dubbings))
	scriptID := int64(0) // 用于获取配音完成状态
	for _, vo := range dubbings {
		userIDs = append(userIDs, vo.UserID)
		characterIDs = append(characterIDs, vo.CharacterID)
		dubbingIDs = append(dubbingIDs, vo.ID)
		assetIDs = append(assetIDs, vo.CharacterAssetID)
		if scriptID == 0 {
			scriptID = vo.ScriptID // 所有配音应该属于同一个剧本
		}
	}

	// 使用单独的上下文和超时设置获取用户信息
	userCtx, cancel := context.WithTimeout(ctx, 2*time.Second)
	defer cancel()
	userInfos, err := batchGetUserInfo(userCtx, userID, userIDs)
	if err != nil {
		logger.Errorf("batchGetUserInfo error: %v, userIDs: %v", err, userIDs)
		userInfos = make(map[int64]*svcscript.UserInfo)
	}

	// 批量获取用户角色配音完成状态
	completionStatusMap := make(map[string]bool) // key: "userID_characterID", value: isComplete
	if scriptID > 0 {
		// 构建需要查询的用户ID列表（去重）
		userIDSet := make(map[int64]struct{})
		for _, vo := range dubbings {
			userIDSet[vo.UserID] = struct{}{}
		}

		// 转换为切片
		userIDs := make([]int64, 0, len(userIDSet))
		for userID := range userIDSet {
			userIDs = append(userIDs, userID)
		}

		// 批量获取所有用户的完成角色列表
		userCompletedCharacters, err := s.dubbingCompletionModel.BatchGetCompletedCharacterIDs(ctx, userIDs, scriptID)
		if err != nil {
			logger.Warnf("BatchGetCompletedCharacterIDs error: %v, userIDs: %v, scriptID: %d", err, userIDs, scriptID)
			// 如果批量查询失败，使用空映射，不影响主流程
			userCompletedCharacters = make(map[int64][]int64)
		}

		// 构建用户-角色完成状态映射
		for _, vo := range dubbings {
			key := fmt.Sprintf("%d_%d", vo.UserID, vo.CharacterID)
			isComplete := false

			if completedCharacterIDs, exists := userCompletedCharacters[vo.UserID]; exists {
				for _, completedCharID := range completedCharacterIDs {
					if completedCharID == vo.CharacterID {
						isComplete = true
						break
					}
				}
			}
			completionStatusMap[key] = isComplete
		}
	}

	// 获取角色信息
	characters, err := s.characterModel.BatchGetCharactersByIDs(ctx, characterIDs)
	if err != nil {
		logger.Errorf("BatchGetCharactersByIDs error: %v", err)
		characters = make(map[int64]*model.Character)
	}

	// 获取角色资源包
	characterAssets, err := s.characterAssetModel.GetAssetsMapByIDs(ctx, assetIDs)
	if err != nil {
		logger.Errorf("GetAssetsMapByIDs error: %v", err)
		characterAssets = make(map[int64]*model.CharacterAsset)
	}

	// 获取评论数量
	commentCounts, err := s.commentModel.BatchGetCommentCountByDubbingIDs(ctx, userID, dubbingIDs)
	if err != nil {
		logger.Errorf("BatchGetCommentCountByDubbingIDs error: %v", err)
		commentCounts = make(map[int64]int32)
	}

	// 获取用户点赞状态
	var userLikedStatus map[int64]bool
	if userID > 0 {
		userLikedStatus, err = s.likeModel.BatchIsLiked(ctx, userID, dubbingIDs, svcscript.LikeType_LIKE_TYPE_DUBBING)
		if err != nil {
			logger.Errorf("BatchIsLiked error: %v", err)
			userLikedStatus = make(map[int64]bool)
		}
	} else {
		userLikedStatus = make(map[int64]bool)
	}

	// 组装返回数据
	pbDubbings := make([]*svcscript.Dubbing, 0, len(dubbings))
	isDubbing := false

	for _, vo := range dubbings {
		character, ok := characters[vo.CharacterID]
		if !ok {
			logger.Infof("voice over %d has no character", vo.ID)
			continue
		}

		userInfo, ok := userInfos[vo.UserID]
		if !ok {
			// 如果用户信息获取失败，创建一个基本的用户信息
			userInfo = &svcscript.UserInfo{
				UserId:   vo.UserID,
				Nickname: "未知用户",
			}
		}

		// 设置用户角色配音完成状态
		completionKey := fmt.Sprintf("%d_%d", vo.UserID, vo.CharacterID)
		if isComplete, exists := completionStatusMap[completionKey]; exists {
			userInfo.IsCompleted = isComplete
		} else {
			userInfo.IsCompleted = false
		}

		protoChar := &svcscript.Character{}
		var asset *model.CharacterAsset
		if _, ok := characterAssets[vo.CharacterAssetID]; ok {
			asset = characterAssets[vo.CharacterAssetID]
		}
		// 如果评论绑定的角色资源未找到，使用角色默认资源兜底
		if asset == nil {
			logger.Infof("Comment %d has no asset, use character default asset, characterId:%v", vo.ID, vo.CharacterID)
			for _, item := range characterAssets {
				if item.IsDefault {
					asset = item
				}
			}
			if asset == nil {
				logger.Infof("Comment %d has no asset, default asset is empty, characterId:%v", vo.ID, vo.CharacterID)
				continue
			}
		}
		protoChar = s.characterSvc.ConvertToProtoCharacter(character, asset)

		commentCount, ok := commentCounts[vo.ID]
		if !ok {
			commentCount = 0
		}

		pbDubbing := &svcscript.Dubbing{
			Id:               vo.ID,
			ScriptId:         vo.ScriptID,
			LineId:           vo.LineID,
			UserId:           vo.UserID,
			CharacterId:      vo.CharacterID,
			CharacterAssetId: vo.CharacterAssetID,
			OriginalUrl:      vo.GetFullOriginalURL(),
			OriginalDuration: vo.OriginalDuration,
			DubbedUrl:        vo.GetFullDubbedURL(),
			DubbedDuration:   vo.DubbedDuration,
			IsAuthor:         vo.IsAuthor,
			IsTop:            vo.IsTop,
			Status:           vo.Status,
			CreatedAt:        vo.CreatedAt,
			UpdatedAt:        vo.UpdatedAt,
			UserInfo:         userInfo,
			Character:        protoChar,
			LikeCount:        vo.Likes,
			CommentCount:     commentCount,
			ReviewStatus:     vo.ReviewStatus,
		}

		// 用户点赞状态
		if userLikedStatus != nil {
			if status, ok := userLikedStatus[vo.ID]; ok {
				pbDubbing.IsLiked = status
			}
		}

		pbDubbings = append(pbDubbings, pbDubbing)

		if vo.UserID == userID {
			isDubbing = true
		}
	}

	return pbDubbings, isDubbing, nil
}

// getCompletedUserIDsForLine 获取已完成该台词角色配音的用户ID列表
func (s *DubbingService) getCompletedUserIDsForLine(ctx context.Context, lineID, scriptID int64) ([]int64, error) {
	// 1. 获取该台词的角色ID
	line, err := s.lineModel.GetLineByID(ctx, lineID)
	if err != nil {
		return nil, err
	}
	if line == nil {
		return []int64{}, nil
	}

	// 2. 查询已完成该角色配音的用户ID列表
	userIDs, err := s.dubbingCompletionModel.GetCompletedUserIDsByCharacter(ctx, scriptID, line.CharacterID)
	if err != nil {
		logger.Errorf("getCompletedUserIDsForLine error: %v", err)
		return nil, err
	}

	return userIDs, nil
}

// prioritizeBatchDubbings 将指定批次的配音置顶
func (s *DubbingService) prioritizeBatchDubbings(dubbings []*model.Dubbing, batchDubbingIDs []int64) []*model.Dubbing {
	if len(batchDubbingIDs) == 0 {
		return dubbings
	}

	// 创建批次配音ID的映射，便于快速查找
	batchIDMap := make(map[int64]bool)
	for _, id := range batchDubbingIDs {
		batchIDMap[id] = true
	}

	// 分离批次配音和其他配音
	var batchDubbings []*model.Dubbing
	var otherDubbings []*model.Dubbing

	for _, dubbing := range dubbings {
		if batchIDMap[dubbing.ID] {
			batchDubbings = append(batchDubbings, dubbing)
		} else {
			otherDubbings = append(otherDubbings, dubbing)
		}
	}

	// 将批次配音放在前面，其他配音放在后面
	result := make([]*model.Dubbing, 0, len(dubbings))
	result = append(result, batchDubbings...)
	result = append(result, otherDubbings...)

	return result
}

// processDubbings 统一处理配音列表，获取关联的用户信息、角色信息等
func (s *DubbingService) processDubbingsOld(ctx context.Context, dubbings []*model.Dubbing, userID int64) ([]*svcscript.Dubbing, bool, error) {
	// 收集需要的ID列表
	userIDs := make([]int64, 0, len(dubbings))
	characterIDs := make([]int64, 0, len(dubbings))
	assetIDs := make([]int64, 0, len(dubbings))
	dubbingIDs := make([]int64, 0, len(dubbings))
	for _, vo := range dubbings {
		userIDs = append(userIDs, vo.UserID)
		characterIDs = append(characterIDs, vo.CharacterID)
		dubbingIDs = append(dubbingIDs, vo.ID)
		assetIDs = append(assetIDs, vo.CharacterAssetID)
	}

	// 并发批量获取关联数据
	var (
		userInfos       map[int64]*svcscript.UserInfo
		characters      map[int64]*model.Character
		characterAssets map[int64]*model.CharacterAsset
		commentCounts   map[int64]int32
		userLikedStatus map[int64]bool
		isDubbing       bool
		err             error
	)

	eg, egCtx := errgroup.WithContext(ctx)

	// 批量获取用户信息
	eg.Go(func() error {
		var e error
		userInfos, e = batchGetUserInfo(egCtx, userID, userIDs)
		if e != nil {
			logger.Errorf("batchGetUserInfo error: %v, userIDs: %v", e, userIDs)
			userInfos = make(map[int64]*svcscript.UserInfo)
			return nil
		}
		return nil
	})

	// 批量获取角色信息
	eg.Go(func() error {
		var e error
		characters, e = s.characterModel.BatchGetCharactersByIDs(egCtx, characterIDs)
		return e
	})

	// 批量获取角色资源包
	eg.Go(func() error {
		var e error
		characterAssets, e = s.characterAssetModel.GetAssetsMapByIDs(egCtx, assetIDs)
		return e
	})

	// 批量获取评论数量
	eg.Go(func() error {
		var e error
		commentCounts, e = s.commentModel.BatchGetCommentCountByDubbingIDs(egCtx, userID, dubbingIDs)
		return e
	})

	// 批量获取用户点赞状态
	if userID > 0 {
		eg.Go(func() error {
			var err error
			userLikedStatus, err = s.likeModel.BatchIsLiked(egCtx, userID, dubbingIDs, svcscript.LikeType_LIKE_TYPE_DUBBING)
			if err != nil {
				return err
			}
			return nil
		})
	}

	// 等待所有查询完成
	if err = eg.Wait(); err != nil {
		return nil, false, err
	}

	if len(userInfos) == 0 {
		logger.Infof("No user info found for userIDs: %v", userIDs)
		return nil, false, nil
	}

	// 组装返回数据
	pbDubbings := make([]*svcscript.Dubbing, 0, len(dubbings))
	for _, vo := range dubbings {
		protoChar := &svcscript.Character{}
		character := characters[vo.CharacterID]
		if character == nil {
			logger.Infof("voice over %d has no character", vo.ID)
			continue
		}
		var asset *model.CharacterAsset
		if _, ok := characterAssets[vo.CharacterAssetID]; ok {
			asset = characterAssets[vo.CharacterAssetID]
		}
		// 如果评论绑定的角色资源未找到，使用角色默认资源兜底
		if asset == nil {
			logger.Infof("Comment %d has no asset, use character default asset, characterId:%v", vo.ID, vo.CharacterID)
			for _, item := range characterAssets {
				if item.IsDefault {
					asset = item
				}
			}
			if asset == nil {
				logger.Infof("Comment %d has no asset, default asset is empty, characterId:%v", vo.ID, vo.CharacterID)
				continue
			}
		}
		protoChar = s.characterSvc.ConvertToProtoCharacter(character, asset)

		userInfo, ok := userInfos[vo.UserID]
		if !ok {
			logger.Infof("voice over %d has no user info", vo.ID)
			continue
		}
		commentCount, ok := commentCounts[vo.ID]
		if !ok {
			commentCount = 0
		}

		pbDubbing := &svcscript.Dubbing{
			Id:               vo.ID,
			ScriptId:         vo.ScriptID,
			LineId:           vo.LineID,
			UserId:           vo.UserID,
			CharacterId:      vo.CharacterID,
			CharacterAssetId: vo.CharacterAssetID,
			OriginalUrl:      vo.GetFullOriginalURL(),
			OriginalDuration: vo.OriginalDuration,
			DubbedUrl:        vo.GetFullDubbedURL(),
			DubbedDuration:   vo.DubbedDuration,
			IsAuthor:         vo.IsAuthor,
			IsTop:            vo.IsTop,
			Status:           vo.Status,
			CreatedAt:        vo.CreatedAt,
			UpdatedAt:        vo.UpdatedAt,
			UserInfo:         userInfo,
			Character:        protoChar,
			LikeCount:        vo.Likes,
			CommentCount:     commentCount,
			ReviewStatus:     vo.ReviewStatus,
		}

		// 用户点赞状态
		if userLikedStatus != nil {
			if status, ok := userLikedStatus[vo.ID]; ok {
				pbDubbing.IsLiked = status
			}
		}

		pbDubbings = append(pbDubbings, pbDubbing)

		if vo.UserID == userID {
			isDubbing = true
		}
	}

	return pbDubbings, isDubbing, nil
}

// CreateDubbing 批量创建配音
func (s *DubbingService) CreateDubbing(ctx context.Context, req *svcscript.CreateDubbingReq) (int64, error) {
	// 频控：每用户每分钟最多5次
	if err := s.limiter.DubbingLimiter(ctx, req.UserId); err != nil {
		return 0, err
	}

	if err := req.Validate(); err != nil {
		return 0, errcode.ErrorParam
	}

	// 检查剧本
	script, err := s.scriptModel.GetScriptByID(ctx, req.ScriptId)
	if err != nil {
		logger.Errorf("GetScriptByID error: %v", err)
		return 0, err
	}

	// 批量创建配音的事务处理
	var batchDubbingId int64
	var eventData = make(map[int64]string)
	var lineDubbingMappings []model.LineDubbingMapping // 用于记录配音流水
	var dubbingCount int32
	var affectedCharacters = make(map[int64]bool) // 记录受影响的角色ID，用于事务后更新完成状态

	err = s.baseModel.Transaction(ctx, func(tx *gorm.DB) error {
		isAuthor := false
		if script.AuthorID == req.UserId {
			isAuthor = true
		}

		now := util.NowTimeMillis()

		// 遍历所有台词的配音列表
		for lineID, lineDubbings := range req.LineDubbings {
			var dubbingIDs []int64 // 当前台词的配音ID列表

			for _, item := range lineDubbings.Items {
				item.DubbedUrl = util.ExtractPath(item.DubbedUrl)
				dubbing := &model.Dubbing{
					ScriptID:         req.ScriptId,
					LineID:           lineID,
					UserID:           req.UserId,
					CharacterID:      item.CharacterId,
					CharacterAssetID: item.CharacterAssetId,
					OriginalURL:      item.OriginalUrl,
					OriginalDuration: item.OriginalDuration,
					DubbedURL:        item.DubbedUrl,
					DubbedDuration:   item.DubbedDuration,
					IsAuthor:         isAuthor,
					IsTop:            false,
					Status:           svcscript.Status_STATUS_ACTIVE,
					ReviewStatus:     svcscript.ReviewStatus_REVIEW_STATUS_PENDING,
					CreatedAt:        now,
					UpdatedAt:        now,
				}

				// 如果没有指定资源包ID，获取角色的默认资源包
				if dubbing.CharacterAssetID == 0 && dubbing.CharacterID > 0 {
					randomAsset, err := s.characterAssetModel.GetRandomAssetByCharacterID(ctx, dubbing.CharacterID)
					if err != nil {
						logger.Warnf("获取角色随机资源包失败: %v, 使用角色ID: %d", err, dubbing.CharacterID)
					} else if randomAsset != nil {
						dubbing.CharacterAssetID = randomAsset.ID
						logger.Infof("配音使用随机资源包: %d, 角色ID: %d", randomAsset.ID, dubbing.CharacterID)
					}
				}

				err := s.dubbingModel.CreateDubbingTx(ctx, tx, dubbing)
				if err != nil {
					logger.Errorf("CreateDubbing error: %v", err)
					return err
				}

				dubbingIDs = append(dubbingIDs, dubbing.ID)
				dubbingCount++

				// 记录受影响的角色ID
				affectedCharacters[dubbing.CharacterID] = true

				// 增加台词配音数
				if err := s.lineModel.IncreaseVoiceCountTx(ctx, tx, dubbing.LineID, 1); err != nil {
					logger.Errorf("增加配音数失败：%v, lineID: %d", err, dubbing.LineID)
				}

				// 触发配音创建事件，更新用户统计数据
				if s.eventPusher != nil {
					if err := s.eventPusher.PushDubbingCreatedEvent(ctx, dubbing.ScriptID, dubbing.CharacterID, dubbing.UserID); err != nil {
						logger.Errorf("分发配音创建事件失败: %v", err)
					}
				}

				// 配音扣费
				if item.DubbedDuration > 0 {
					// DubbedDuration是毫秒，需要先转换为秒，再按每10秒=1分贝计算，不足10秒按10秒计算
					decibel := int64(math.Ceil(float64(item.DubbedDuration)/1000/10)) * 100
					orderID := util.NewOrderID(util.TOrderType(consts.BizTypeScriptDubbing)).String()
					_, err = svcmgr.AccountClient().ConsumeUserScore(ctx, &svcaccount.ConsumeUserScoreReq{
						UserId:        dubbing.UserID,
						Score:         decibel,
						BizType:       int64(consts.BizTypeScriptDubbing),
						BizName:       consts.BizTypeScriptDubbing.String(),
						OrderId:       orderID,
						AllowOverdraw: true,
					})
					if err != nil {
						logger.Errorf("扣费失败: %v, userId: %d, score: %d, bizType: %d, bizName: %s, orderId: %s",
							err, dubbing.UserID, decibel, int64(consts.BizTypeScriptDubbing), consts.BizTypeScriptDubbing.String(), orderID)
						return err
					}
				}

				// 异步配音审核事件数据
				eventData[dubbing.ID] = dubbing.DubbedURL
			}

			// 记录台词配音映射
			if len(dubbingIDs) > 0 {
				lineDubbingMappings = append(lineDubbingMappings, model.LineDubbingMapping{
					LineID:     lineID,
					DubbingIDs: dubbingIDs,
				})
			}
		}

		// 创建配音记录
		if len(lineDubbingMappings) > 0 {
			dubbingRecord := &model.DubbingRecord{
				ScriptID:     req.ScriptId,
				UserID:       req.UserId,
				DubbingCount: dubbingCount,
				Status:       svcscript.Status_STATUS_ACTIVE,
			}

			// 设置台词配音映射
			if err := dubbingRecord.SetLineDubbingMappings(lineDubbingMappings); err != nil {
				logger.Errorf("设置台词配音映射失败: %v", err)
				return err
			}

			// 创建配音记录
			if err := s.dubbingRecordModel.CreateDubbingRecordTx(ctx, tx, dubbingRecord); err != nil {
				logger.Errorf("创建配音记录失败: %v", err)
				return err
			}

			logger.Infof("创建配音记录成功: recordID=%d, scriptID=%d, userID=%d, dubbingCount=%d",
				dubbingRecord.ID, req.ScriptId, req.UserId, dubbingCount)

			// 配音批次id
			batchDubbingId = dubbingRecord.ID
		}

		return nil
	})

	if err != nil {
		logger.Errorf("批量创建配音事务失败: %v", err)
		return 0, err
	}

	// 事务成功后，异步更新受影响角色的配音完成状态
	// 这样可以确保读取到已提交的配音数据，避免事务一致性问题
	if len(affectedCharacters) > 0 {
		util.SafeGo(func() {
			asyncCtx := context.WithoutCancel(ctx)
			for characterID := range affectedCharacters {
				if err := s.dubbingCompletionModel.CheckAndUpdateCharacterCompletionStatus(asyncCtx, req.UserId, req.ScriptId, characterID); err != nil {
					logger.Warnf("异步检查角色配音完成状态失败: %v, userID: %d, scriptID: %d, characterID: %d",
						err, req.UserId, req.ScriptId, characterID)
				}
			}
			logger.Infof("异步更新配音完成状态完成: userID=%d, scriptID=%d, affectedCharacters=%v",
				req.UserId, req.ScriptId, affectedCharacters)
		})
	}

	for dubbingId, dubbedURL := range eventData {
		err := s.eventPusher.PushReviewDubbingEvent(ctx, req.ScriptId, req.UserId, dubbingId, dubbedURL)
		if err != nil {
			logger.Errorf("PushReviewDubbingEvent error: %v", err)
		}
	}

	// 配音创建指标上报
	if err := metric.ReportPv("dubbing.create.count", int64(dubbingCount), nil); err != nil {
		logger.Errorf("Report dubbing create metric error: %v", err)
	}

	return batchDubbingId, nil
}

func (s *DubbingService) NotifyDubbing(ctx context.Context, dubbing *model.Dubbing, dubbingId ...int64) {
	var err error
	if len(dubbingId) > 0 && dubbingId[0] > 0 && dubbing == nil {
		dubbing, err = s.dubbingModel.GetDubbingByID(ctx, dubbingId[0])
		if err != nil {
			logger.Errorf("NotifyDubbing GetDubbingByID error: %v, dubbingID: %d", err, dubbingId[0])
			return
		}
	}

	userInfo, err := getUserInfo(ctx, dubbing.UserID)
	if err != nil {
		logger.Errorf("评论通知未找到用户, userID:%v", userInfo)
		return
	}

	script, err := s.scriptModel.GetScriptByID(ctx, dubbing.ScriptID)
	if err != nil {
		logger.Errorf("not found script, scriptID:%v", dubbing.ScriptID)
		return
	}

	if userInfo.UserId == script.AuthorID {
		logger.Infof("配音作者是剧本作者, 不通知。 dubbingID:%v", dubbing.ID)
		return
	}

	// 通知给剧本作者，剧本作者是否点赞
	isLiked, err := s.likeModel.IsLiked(ctx, script.AuthorID, dubbing.ID, svcscript.LikeType_LIKE_TYPE_DUBBING)
	if err != nil {
		logger.Errorf("not found liked, scriptID:%v", dubbing.ScriptID)
	}

	msgContent := map[string]any{
		"content": &svcchat.TemplateMsg{
			Tpl: "为我的剧本配音",
		},
		"member": svcchat.SimpleMember{
			UserId: userInfo.UserId,
			Avatar: userInfo.Avatar,
			Nick:   userInfo.Nickname,
		},
		"script": svcchat.ScriptInfo{
			ScriptId: script.ID,
			Title:    script.Title,
			Cover:    script.GetFullCover(),
		},
		"dubbing": svcchat.Dubbing{
			DubbingId: dubbing.ID,
			AudioUrl:  dubbing.GetFullDubbedURL(),
			Duration:  int64(dubbing.DubbedDuration),
			LikeCount: dubbing.Likes,
			IsLiked:   isLiked,
		},
		"time": util.NowTimeMillis(),
	}

	// send msg
	msg := &basemsgtransfer.SendMsgReq{
		Msgs: []*basemsgtransfer.MsgData{
			{
				From:        consts.OfficialSecretaryUserid,
				To:          script.AuthorID,
				SessionType: int32(basemsgtransfer.SessionType_SessionTypeSystem),
				ContentType: int32(basemsgtransfer.ContentType_Dubbing),
				Content:     util.JsonStr(msgContent),
				MsgFrom:     uint32(basemsgtransfer.MsgFromEnum_MsgFromIm),
				CreateTime:  util.NowTimeMillis(),
			},
		},
	}
	logger.Infof("dubbing notify msg=%+v", util.JsonStr(msg))
	vResp, tmpErr := svcmgr.BaseMsgTransferClient().SendMsg(ctx, msg)
	if tmpErr != nil || errcode.NotOk(vResp) {
		logger.Errorf("发送配音通知失败:err=%+v vResp=%+v", tmpErr, util.JsonStr(vResp))
	}

}

// AdminDeleteDubbing 管理员删除配音
func (s *DubbingService) AdminDeleteDubbing(ctx context.Context, dubbingID int64) error {
	// 检查配音是否存在
	dubbing, err := s.dubbingModel.GetDubbingByID(ctx, dubbingID)
	if err != nil {
		logger.Errorf("AdminDeleteDubbing GetDubbingByID error: %v, dubbingID: %d", err, dubbingID)
		return err
	}

	err = s.baseModel.Transaction(ctx, func(tx *gorm.DB) error {
		// 执行删除
		err = s.dubbingModel.DeleteDubbingTx(ctx, tx, dubbingID)
		if err != nil {
			logger.Errorf("AdminDeleteDubbing DeleteDubbing error: %v, dubbingID: %d", err, dubbingID)
			return err
		}

		// 减少台词配音数
		if err := s.lineModel.IncreaseVoiceCountTx(ctx, tx, dubbing.LineID, -1); err != nil {
			logger.Errorf("增加配音数失败：%v, lineID: %d", err, dubbing.LineID)
		}

		// 触发配音删除事件，更新用户统计数据
		if s.eventPusher != nil {
			if err := s.eventPusher.PushDubbingDeletedEvent(ctx, dubbing.ScriptID, dubbing.CharacterID, dubbing.UserID); err != nil {
				logger.Errorf("分发配音删除事件失败: %v", err)
			}
		}
		return nil
	})
	if err != nil {
		logger.Errorf("AdminDeleteDubbing error: %v", err)
		return err
	}

	// 事务成功后，异步更新角色配音完成状态
	// 这样可以确保读取到已提交的删除操作，避免事务一致性问题
	util.SafeGo(func() {
		asyncCtx := context.WithoutCancel(ctx)
		if err := s.dubbingCompletionModel.CheckAndUpdateCharacterCompletionStatus(asyncCtx, dubbing.UserID, dubbing.ScriptID, dubbing.CharacterID); err != nil {
			logger.Warnf("异步检查角色配音完成状态失败: %v, userID: %d, scriptID: %d, characterID: %d",
				err, dubbing.UserID, dubbing.ScriptID, dubbing.CharacterID)
		} else {
			logger.Infof("异步更新配音完成状态完成: userID=%d, scriptID=%d, characterID=%d",
				dubbing.UserID, dubbing.ScriptID, dubbing.CharacterID)
		}
	})

	return nil
}

// ReDubbingByAI 重新配音
func (s *DubbingService) ReDubbingByAI(ctx context.Context, req *svcscript.ReDubbingByAIReq) (*svcscript.ReDubbingByAIResp, error) {
	logger.Infof("re dubbing by ai event start, lineID:%v", req.LineId)

	line, err := s.lineModel.GetLineByID(ctx, req.LineId)
	if err != nil {
		logger.Errorf("GetLineByID error: %v, lineID:%v", err, req.LineId)
		return nil, err
	}
	if line.CharacterAssetID == 0 {
		logger.Errorf("line has no character asset, lineID:%v", line.ID)
		return nil, errcode.ErrorParam
	}

	asset, err := s.characterAssetModel.GetAssetByID(ctx, line.CharacterAssetID)
	if err != nil {
		logger.Errorf("GetAssetByID error: %v, lineID:%v", err, line.ID)
		return nil, err
	}

	if asset == nil {
		logger.Errorf("asset not found, lineID:%v, characterID:%v, characterAssetId:%v",
			line.ID, line.CharacterID, line.CharacterAssetID)
		return nil, errcode.ErrorParam
	}

	resp, tmpErr := svcmgr.VcxxjobClient().AsyncTTS(ctx, &vcxxjob.AsyncTTSReq{
		ReferAudioUrl:  asset.SampleAudio,
		ReferAudioText: asset.SampleAudioText,
		Text:           line.Content,
		Source:         0,
		Ext: util.JsonStr(map[string]interface{}{
			"line_id":            line.ID,
			"character_id":       line.CharacterID,
			"character_asset_id": line.CharacterAssetID,
			"script_id":          line.ScriptID,
		}),
		UseVc:     true,
		UseRvc:    asset.ReferenceAudioUseRvc == 1,
		TtsEngine: asset.TtsEngine,
		OutId:     fmt.Sprintf("%d", line.ID),
	})
	if tmpErr != nil {
		logger.Errorf("AsyncTTS error: %v, lineID:%v", tmpErr, line.ID)
		return nil, tmpErr
	}
	logger.Infof("AsyncTTS resp: %+v", resp)
	return &svcscript.ReDubbingByAIResp{}, nil
}

// DeleteDubbingRecord 删除配音记录
func (s *DubbingService) DeleteDubbingRecord(ctx context.Context, req *svcscript.DeleteDubbingRecordReq) (*svcscript.DeleteDubbingRecordResp, error) {
	// 验证配音记录是否存在且属于该用户
	record, err := s.dubbingRecordModel.GetDubbingRecordByID(ctx, req.DubbingRecordId)
	if err != nil {
		logger.Errorf("GetDubbingRecordByID error: %v, recordID: %d", err, req.DubbingRecordId)
		return nil, err
	}

	// 检查权限：只有记录的创建者才能删除
	if record.UserID != req.UserId {
		logger.Warnf("用户无权删除配音记录: userID=%d, recordID=%d, recordUserID=%d",
			req.UserId, req.DubbingRecordId, record.UserID)
		return nil, errcode.ErrPermissionDenied
	}

	// 解析配音记录中的台词配音映射
	lineDubbingMappings, err := record.GetLineDubbingMappings()
	if err != nil {
		logger.Errorf("GetLineDubbingMappings error: %v, recordID: %d", err, req.DubbingRecordId)
		return nil, err
	}

	// 收集所有需要删除的配音ID
	var allDubbingIDs []int64
	for _, mapping := range lineDubbingMappings {
		allDubbingIDs = append(allDubbingIDs, mapping.DubbingIDs...)
	}

	// 如果有配音需要删除，先获取配音详情用于后续处理
	logger.Infof("allDubbingIDs: %v", allDubbingIDs)
	var dubbingsToDelete []*model.Dubbing
	if len(allDubbingIDs) > 0 {
		dubbingsToDelete, err = s.dubbingModel.GetDubbingsByIDs(ctx, allDubbingIDs)
		if err != nil {
			logger.Errorf("GetDubbingsByIDs error: %v, dubbingIDs: %v", err, allDubbingIDs)
			return nil, err
		}
	}

	// 使用事务执行删除操作
	err = s.baseModel.Transaction(ctx, func(tx *gorm.DB) error {
		// 1. 删除配音记录本身
		err = s.dubbingRecordModel.DeleteDubbingRecord(ctx, req.DubbingRecordId)
		if err != nil {
			logger.Errorf("DeleteDubbingRecord error: %v, recordID: %d", err, req.DubbingRecordId)
			return err
		}

		// 2. 如果有配音需要删除，执行批量删除配音
		if len(allDubbingIDs) > 0 {
			// 批量软删除配音
			err = s.dubbingModel.BatchDeleteDubbingsTx(ctx, tx, allDubbingIDs)
			if err != nil {
				logger.Errorf("BatchDeleteDubbingsTx error: %v, dubbingIDs: %v", err, allDubbingIDs)
				return err
			}

			// 3. 批量减少台词配音数
			lineCountMap := make(map[int64]int64)
			for _, dubbing := range dubbingsToDelete {
				lineCountMap[dubbing.LineID]--
			}

			if len(lineCountMap) > 0 {
				err = s.lineModel.BatchIncreaseVoiceCountTx(ctx, tx, lineCountMap)
				if err != nil {
					logger.Errorf("BatchIncreaseVoiceCountTx error: %v, lineCountMap: %v", err, lineCountMap)
					// 继续执行，不中断流程
				}
			}

			// 4. 收集需要异步处理的用户角色信息
			userCharacterMap := make(map[string]bool) // key: "userID_scriptID_characterID"
			for _, dubbing := range dubbingsToDelete {
				key := fmt.Sprintf("%d_%d_%d", dubbing.UserID, dubbing.ScriptID, dubbing.CharacterID)
				userCharacterMap[key] = true
			}
		}

		return nil
	})

	if err != nil {
		logger.Errorf("DeleteDubbingRecord transaction error: %v", err)
		return nil, err
	}

	// 异步处理角色配音完成状态更新和事件推送
	if len(allDubbingIDs) > 0 {
		util.SafeGo(func() {
			asyncCtx := context.WithoutCancel(ctx)

			// 收集需要处理的用户角色信息
			userCharacterMap := make(map[string]bool) // key: "userID_scriptID_characterID"
			for _, dubbing := range dubbingsToDelete {
				key := fmt.Sprintf("%d_%d_%d", dubbing.UserID, dubbing.ScriptID, dubbing.CharacterID)
				userCharacterMap[key] = true
			}

			// 异步更新角色配音完成状态
			for key := range userCharacterMap {
				parts := strings.Split(key, "_")
				if len(parts) != 3 {
					continue
				}
				userID, _ := strconv.ParseInt(parts[0], 10, 64)
				scriptID, _ := strconv.ParseInt(parts[1], 10, 64)
				characterID, _ := strconv.ParseInt(parts[2], 10, 64)

				if err := s.dubbingCompletionModel.CheckAndUpdateCharacterCompletionStatus(asyncCtx, userID, scriptID, characterID); err != nil {
					logger.Warnf("异步更新角色配音完成状态失败: %v, userID: %d, scriptID: %d, characterID: %d",
						err, userID, scriptID, characterID)
				}
			}

			// 异步触发配音删除事件
			if s.eventPusher != nil {
				for key := range userCharacterMap {
					parts := strings.Split(key, "_")
					if len(parts) != 3 {
						continue
					}
					userID, _ := strconv.ParseInt(parts[0], 10, 64)
					scriptID, _ := strconv.ParseInt(parts[1], 10, 64)
					characterID, _ := strconv.ParseInt(parts[2], 10, 64)

					if err := s.eventPusher.PushDubbingDeletedEvent(asyncCtx, scriptID, characterID, userID); err != nil {
						logger.Errorf("异步推送配音删除事件失败: %v, scriptID: %d, characterID: %d, userID: %d",
							err, scriptID, characterID, userID)
					}
				}
			}

			logger.Infof("异步处理完成: recordID=%d, 处理用户角色组合数=%d", req.DubbingRecordId, len(userCharacterMap))
		})
	}

	logger.Infof("配音记录删除成功: recordID=%d, userID=%d, deletedDubbingCount=%d",
		req.DubbingRecordId, req.UserId, len(allDubbingIDs))

	return &svcscript.DeleteDubbingRecordResp{
		Base: errcode.ErrOK.ToSvcBaseResp(),
	}, nil
}

// batchGetLineDubbingsForSimple 批量获取台词的简略配音数据
func (s *DubbingService) batchGetLineDubbingsForSimple(ctx context.Context, lineIDs []int64, userID, scriptID int64, recordLineDubbingMap map[int64][]int64) (map[int64][]*model.Dubbing, error) {
	if len(lineIDs) == 0 {
		return make(map[int64][]*model.Dubbing), nil
	}

	// 批量获取所有台词的配音
	allDubbingsMap, err := s.dubbingModel.BatchGetLineDubbingsForSimple(ctx, lineIDs, userID)
	if err != nil {
		return nil, err
	}

	// 对每个台词的配音应用排序和限制
	for lineID, dubbings := range allDubbingsMap {
		if len(dubbings) > 0 {
			// 获取该台词的批次配音ID
			var batchDubbingIDs []int64
			if recordLineDubbingMap != nil {
				if recordDubbingIDs, exists := recordLineDubbingMap[lineID]; exists {
					batchDubbingIDs = recordDubbingIDs
				}
			}

			// 应用排序逻辑并限制数量
			sortedDubbings := s.applySortingLogicForSimple(dubbings, userID, batchDubbingIDs, consts.DubbingUserLimit)
			allDubbingsMap[lineID] = sortedDubbings
		}
	}

	return allDubbingsMap, nil
}

// DubbingSortConfig 配音排序配置
type DubbingSortConfig struct {
	EnableBatchGroup   bool  // 是否启用批次置顶组
	EnableUserDedup    bool  // 是否启用用户去重
	Limit              int32 // 数量限制，0表示不限制
	BatchAllowMultiple bool  // 批次组是否允许多条
}

// applySortingLogicForSimple 为简略配音列表应用排序逻辑
// 实现4个优先级分组的排序规则：批次置顶 > 当前用户 > 置顶 > 其它
func (s *DubbingService) applySortingLogicForSimple(dubbings []*model.Dubbing, userID int64, batchDubbingIDs []int64, limit int32) []*model.Dubbing {
	config := DubbingSortConfig{
		EnableBatchGroup:   true,
		EnableUserDedup:    true,
		Limit:              limit,
		BatchAllowMultiple: true,
	}
	return s.applySortingLogicUnified(dubbings, userID, batchDubbingIDs, config)
}

// applySortingLogicForComplete 为完整配音列表应用排序逻辑
// 实现3个优先级分组的排序规则：当前用户 > 置顶 > 其它（无批次置顶组）
func (s *DubbingService) applySortingLogicForComplete(dubbings []*model.Dubbing, userID int64, batchDubbingIDs []int64) []*model.Dubbing {
	config := DubbingSortConfig{
		EnableBatchGroup:   false, // 完整列表不受批次影响
		EnableUserDedup:    false, // 完整列表不去重
		Limit:              0,     // 不限制数量
		BatchAllowMultiple: false,
	}
	return s.applySortingLogicUnified(dubbings, userID, batchDubbingIDs, config)
}

// applySortingLogicUnified 统一的配音排序逻辑（简化为4组）
func (s *DubbingService) applySortingLogicUnified(dubbings []*model.Dubbing, userID int64, batchDubbingIDs []int64, config DubbingSortConfig) []*model.Dubbing {
	if len(dubbings) == 0 {
		return []*model.Dubbing{}
	}

	// 创建批次配音ID映射，便于快速查找
	batchIDMap := make(map[int64]bool)
	for _, id := range batchDubbingIDs {
		batchIDMap[id] = true
	}

	// 全局用户去重映射（用于简略列表）
	globalSeenUserIDs := make(map[int64]bool)

	// 第一组：【批次置顶组】- 按批次顺序排列
	var batchDubbings []*model.Dubbing
	if config.EnableBatchGroup && len(batchDubbingIDs) > 0 {
		if config.BatchAllowMultiple {
			// 简略列表：显示批次中的所有配音
			for _, batchID := range batchDubbingIDs {
				for _, dubbing := range dubbings {
					if dubbing.ID == batchID {
						batchDubbings = append(batchDubbings, dubbing)
						if config.EnableUserDedup {
							globalSeenUserIDs[dubbing.UserID] = true
						}
						break
					}
				}
			}
		} else {
			// 完整列表：最多1条（实际上完整列表不启用批次组）
			for _, batchID := range batchDubbingIDs {
				for _, dubbing := range dubbings {
					if dubbing.ID == batchID {
						batchDubbings = append(batchDubbings, dubbing)
						if config.EnableUserDedup {
							globalSeenUserIDs[dubbing.UserID] = true
						}
						break
					}
				}
				if len(batchDubbings) >= 1 {
					break
				}
			}
		}
	}

	// 第二组：【当前用户组】- 按创建时间倒序，取最新1条
	var userDubbings []*model.Dubbing
	var userCandidates []*model.Dubbing
	for _, dubbing := range dubbings {
		if dubbing.UserID == userID && !batchIDMap[dubbing.ID] {
			userCandidates = append(userCandidates, dubbing)
		}
	}
	// 按创建时间倒序排序，取最新1条
	if len(userCandidates) > 0 {
		sort.Slice(userCandidates, func(i, j int) bool {
			return userCandidates[i].CreatedAt > userCandidates[j].CreatedAt
		})

		// 检查用户去重
		if !config.EnableUserDedup || !globalSeenUserIDs[userID] {
			userDubbings = append(userDubbings, userCandidates[0])
			if config.EnableUserDedup {
				globalSeenUserIDs[userID] = true
			}
		}
	}

	// 首页展示特殊逻辑：如果只需要1条配音
	if config.Limit == 1 {
		// 优先返回批次配音
		if len(batchDubbings) > 0 {
			return batchDubbings[:1]
		}
		if len(userDubbings) > 0 {
			return userDubbings[:1]
		}
		// 如果用户没有配音，查找置顶配音
		for _, dubbing := range dubbings {
			if dubbing.UserID != userID && dubbing.IsTop && !batchIDMap[dubbing.ID] {
				if !config.EnableUserDedup || !globalSeenUserIDs[dubbing.UserID] {
					return []*model.Dubbing{dubbing}
				}
			}
		}
	}

	// 第三组：【置顶组】- 按创建时间倒序，取最优1条
	var toppedDubbings []*model.Dubbing
	var toppedCandidates []*model.Dubbing

	// 收集已在前面组中处理的配音ID，避免重复
	processedDubbingIDs := make(map[int64]bool)
	for _, vo := range batchDubbings {
		processedDubbingIDs[vo.ID] = true
	}
	for _, vo := range userDubbings {
		processedDubbingIDs[vo.ID] = true
	}

	for _, dubbing := range dubbings {
		if dubbing.IsTop && !batchIDMap[dubbing.ID] && !processedDubbingIDs[dubbing.ID] {
			// 检查用户去重
			if !config.EnableUserDedup || !globalSeenUserIDs[dubbing.UserID] {
				toppedCandidates = append(toppedCandidates, dubbing)
			}
		}
	}

	// 按创建时间倒序排序，取最优1条
	if len(toppedCandidates) > 0 {
		sort.Slice(toppedCandidates, func(i, j int) bool {
			return toppedCandidates[i].CreatedAt > toppedCandidates[j].CreatedAt
		})

		// 取第一条（最优）
		toppedDubbings = append(toppedDubbings, toppedCandidates[0])
		if config.EnableUserDedup {
			globalSeenUserIDs[toppedCandidates[0].UserID] = true
		}
	}

	// 第四组：【其它组】- 包含所有剩余配音，按点赞数倒序，创建时间倒序
	var otherDubbings []*model.Dubbing

	// 更新已处理的配音ID集合，包含置顶组的配音
	for _, vo := range toppedDubbings {
		processedDubbingIDs[vo.ID] = true
	}

	var otherCandidates []*model.Dubbing
	for _, dubbing := range dubbings {
		if !batchIDMap[dubbing.ID] && !processedDubbingIDs[dubbing.ID] {
			// 检查用户去重（仅简略列表）
			if !config.EnableUserDedup || !globalSeenUserIDs[dubbing.UserID] {
				otherCandidates = append(otherCandidates, dubbing)
			}
		}
	}

	// 按点赞数倒序，创建时间倒序排序
	sort.Slice(otherCandidates, func(i, j int) bool {
		if otherCandidates[i].Likes != otherCandidates[j].Likes {
			return otherCandidates[i].Likes > otherCandidates[j].Likes // likes DESC
		}
		return otherCandidates[i].CreatedAt > otherCandidates[j].CreatedAt // created_at DESC
	})

	otherDubbings = otherCandidates

	// 合并所有结果
	combinedResults := make([]*model.Dubbing, 0)

	// 按优先级顺序合并前3组
	if config.EnableBatchGroup {
		combinedResults = append(combinedResults, batchDubbings...)
	}
	combinedResults = append(combinedResults, userDubbings...)
	combinedResults = append(combinedResults, toppedDubbings...)

	// 检查前3组是否已达到限制
	if config.Limit > 0 && int32(len(combinedResults)) >= config.Limit {
		return combinedResults[:config.Limit]
	}

	// 添加第4组【其它组】配音
	if config.EnableUserDedup {
		// 简略列表：按用户ID去重
		for _, vo := range otherDubbings {
			if !globalSeenUserIDs[vo.UserID] {
				combinedResults = append(combinedResults, vo)
				globalSeenUserIDs[vo.UserID] = true
				// 检查是否已达到限制
				if config.Limit > 0 && int32(len(combinedResults)) >= config.Limit {
					return combinedResults[:config.Limit]
				}
			}
		}
	} else {
		// 完整列表：不去重，直接添加
		combinedResults = append(combinedResults, otherDubbings...)
	}

	return combinedResults
}

// collectAllIDsFromDubbingsMap 从配音映射中收集所有需要的ID
func (s *DubbingService) collectAllIDsFromDubbingsMap(dubbingsMap map[int64][]*model.Dubbing) ([]int64, []int64, []int64, []int64) {
	userIDSet := make(map[int64]struct{})
	characterIDSet := make(map[int64]struct{})
	assetIDSet := make(map[int64]struct{})
	dubbingIDSet := make(map[int64]struct{})

	for _, dubbings := range dubbingsMap {
		for _, dubbing := range dubbings {
			userIDSet[dubbing.UserID] = struct{}{}
			characterIDSet[dubbing.CharacterID] = struct{}{}
			dubbingIDSet[dubbing.ID] = struct{}{}
			if dubbing.CharacterAssetID > 0 {
				assetIDSet[dubbing.CharacterAssetID] = struct{}{}
			}
		}
	}

	// 转换为切片
	userIDs := make([]int64, 0, len(userIDSet))
	characterIDs := make([]int64, 0, len(characterIDSet))
	assetIDs := make([]int64, 0, len(assetIDSet))
	dubbingIDs := make([]int64, 0, len(dubbingIDSet))

	for userID := range userIDSet {
		userIDs = append(userIDs, userID)
	}
	for characterID := range characterIDSet {
		characterIDs = append(characterIDs, characterID)
	}
	for assetID := range assetIDSet {
		assetIDs = append(assetIDs, assetID)
	}
	for dubbingID := range dubbingIDSet {
		dubbingIDs = append(dubbingIDs, dubbingID)
	}

	return userIDs, characterIDs, assetIDs, dubbingIDs
}

// BatchData 批量数据结构
type BatchData struct {
	UserInfos           map[int64]*svcscript.UserInfo
	Characters          map[int64]*model.Character
	CharacterAssets     map[int64]*model.CharacterAsset
	CommentCounts       map[int64]int32
	UserLikedStatus     map[int64]bool
	CompletionStatusMap map[string]bool // key: "userID_characterID", value: isComplete
}

// batchGetAllRelatedData 批量获取所有关联数据
func (s *DubbingService) batchGetAllRelatedData(ctx context.Context, userID, scriptID int64, allUserIDs, allCharacterIDs, allAssetIDs, allDubbingIDs []int64) (*BatchData, error) {
	batchData := &BatchData{}

	// 使用 errgroup 并发获取数据
	eg, egCtx := errgroup.WithContext(ctx)

	// 获取用户信息
	eg.Go(func() error {
		userCtx, cancel := context.WithTimeout(egCtx, 2*time.Second)
		defer cancel()
		userInfos, err := batchGetUserInfo(userCtx, userID, allUserIDs)
		if err != nil {
			logger.Errorf("batchGetUserInfo error: %v", err)
			batchData.UserInfos = make(map[int64]*svcscript.UserInfo)
		} else {
			batchData.UserInfos = userInfos
		}
		return nil // 不返回错误，使用空映射继续
	})

	// 获取角色信息
	eg.Go(func() error {
		characters, err := s.characterModel.BatchGetCharactersByIDs(egCtx, allCharacterIDs)
		if err != nil {
			logger.Errorf("BatchGetCharactersByIDs error: %v", err)
			batchData.Characters = make(map[int64]*model.Character)
		} else {
			batchData.Characters = characters
		}
		return nil
	})

	// 获取角色资源包
	eg.Go(func() error {
		characterAssets, err := s.characterAssetModel.GetAssetsMapByIDs(egCtx, allAssetIDs)
		if err != nil {
			logger.Errorf("GetAssetsMapByIDs error: %v", err)
			batchData.CharacterAssets = make(map[int64]*model.CharacterAsset)
		} else {
			batchData.CharacterAssets = characterAssets
		}
		return nil
	})

	// 获取评论数量
	eg.Go(func() error {
		commentCounts, err := s.commentModel.BatchGetCommentCountByDubbingIDs(egCtx, userID, allDubbingIDs)
		if err != nil {
			logger.Errorf("BatchGetCommentCountByDubbingIDs error: %v", err)
			batchData.CommentCounts = make(map[int64]int32)
		} else {
			batchData.CommentCounts = commentCounts
		}
		return nil
	})

	// 获取用户点赞状态
	if userID > 0 {
		eg.Go(func() error {
			userLikedStatus, err := s.likeModel.BatchIsLiked(egCtx, userID, allDubbingIDs, svcscript.LikeType_LIKE_TYPE_DUBBING)
			if err != nil {
				logger.Errorf("BatchIsLiked error: %v", err)
				batchData.UserLikedStatus = make(map[int64]bool)
			} else {
				batchData.UserLikedStatus = userLikedStatus
			}
			return nil
		})
	} else {
		batchData.UserLikedStatus = make(map[int64]bool)
	}

	// 获取配音完成状态
	eg.Go(func() error {
		// 构建需要查询的用户ID列表（去重）
		userIDSet := make(map[int64]struct{})
		for _, userID := range allUserIDs {
			userIDSet[userID] = struct{}{}
		}

		userIDs := make([]int64, 0, len(userIDSet))
		for uID := range userIDSet {
			userIDs = append(userIDs, uID)
		}

		// 批量获取所有用户的完成角色列表
		userCompletedCharacters, err := s.dubbingCompletionModel.BatchGetCompletedCharacterIDs(egCtx, userIDs, scriptID)
		if err != nil {
			logger.Warnf("BatchGetCompletedCharacterIDs error: %v", err)
			batchData.CompletionStatusMap = make(map[string]bool)
		} else {
			// 构建完成状态映射
			completionStatusMap := make(map[string]bool)
			for _, userID := range allUserIDs {
				for _, characterID := range allCharacterIDs {
					key := fmt.Sprintf("%d_%d", userID, characterID)
					isComplete := false

					if completedCharacterIDs, exists := userCompletedCharacters[userID]; exists {
						for _, completedCharID := range completedCharacterIDs {
							if completedCharID == characterID {
								isComplete = true
								break
							}
						}
					}
					completionStatusMap[key] = isComplete
				}
			}
			batchData.CompletionStatusMap = completionStatusMap
		}
		return nil
	})

	// 等待所有查询完成
	if err := eg.Wait(); err != nil {
		return nil, err
	}

	return batchData, nil
}

// assembleDubbingsWithBatchData 使用预加载的批量数据组装配音信息
func (s *DubbingService) assembleDubbingsWithBatchData(dubbings []*model.Dubbing, userID int64, batchData *BatchData) ([]*svcscript.Dubbing, bool) {
	if len(dubbings) == 0 {
		return []*svcscript.Dubbing{}, false
	}

	pbDubbings := make([]*svcscript.Dubbing, 0, len(dubbings))
	isDubbing := false

	for _, vo := range dubbings {
		character, ok := batchData.Characters[vo.CharacterID]
		if !ok {
			logger.Infof("voice over %d has no character", vo.ID)
			continue
		}

		userInfo, ok := batchData.UserInfos[vo.UserID]
		if !ok {
			// 如果用户信息获取失败，创建一个基本的用户信息
			userInfo = &svcscript.UserInfo{
				UserId:   vo.UserID,
				Nickname: "未知用户",
			}
		}

		// 设置用户角色配音完成状态
		completionKey := fmt.Sprintf("%d_%d", vo.UserID, vo.CharacterID)
		if isComplete, exists := batchData.CompletionStatusMap[completionKey]; exists {
			userInfo.IsCompleted = isComplete
		} else {
			userInfo.IsCompleted = false
		}

		protoChar := &svcscript.Character{}
		var asset *model.CharacterAsset
		if _, ok := batchData.CharacterAssets[vo.CharacterAssetID]; ok {
			asset = batchData.CharacterAssets[vo.CharacterAssetID]
		}
		// 如果配音绑定的角色资源未找到，使用角色默认资源兜底
		if asset == nil {
			logger.Infof("Dubbing %d has no asset, use character default asset, characterId:%v", vo.ID, vo.CharacterID)
			for _, item := range batchData.CharacterAssets {
				if item.IsDefault && item.CharacterID == vo.CharacterID {
					asset = item
					break
				}
			}
			if asset == nil {
				logger.Infof("Dubbing %d has no asset, default asset is empty, characterId:%v", vo.ID, vo.CharacterID)
				continue
			}
		}
		protoChar = s.characterSvc.ConvertToProtoCharacter(character, asset)

		commentCount, ok := batchData.CommentCounts[vo.ID]
		if !ok {
			commentCount = 0
		}

		pbDubbing := &svcscript.Dubbing{
			Id:               vo.ID,
			ScriptId:         vo.ScriptID,
			LineId:           vo.LineID,
			UserId:           vo.UserID,
			CharacterId:      vo.CharacterID,
			CharacterAssetId: vo.CharacterAssetID,
			OriginalUrl:      vo.GetFullOriginalURL(),
			OriginalDuration: vo.OriginalDuration,
			DubbedUrl:        vo.GetFullDubbedURL(),
			DubbedDuration:   vo.DubbedDuration,
			IsAuthor:         vo.IsAuthor,
			IsTop:            vo.IsTop,
			Status:           vo.Status,
			CreatedAt:        vo.CreatedAt,
			UpdatedAt:        vo.UpdatedAt,
			UserInfo:         userInfo,
			Character:        protoChar,
			LikeCount:        vo.Likes,
			CommentCount:     commentCount,
			ReviewStatus:     vo.ReviewStatus,
		}

		// 用户点赞状态
		if batchData.UserLikedStatus != nil {
			if status, ok := batchData.UserLikedStatus[vo.ID]; ok {
				pbDubbing.IsLiked = status
			}
		}

		pbDubbings = append(pbDubbings, pbDubbing)

		if vo.UserID == userID {
			isDubbing = true
		}
	}

	return pbDubbings, isDubbing
}

// batchGetLinesByScriptIDs 批量获取多个剧本的台词
func (s *DubbingService) batchGetLinesByScriptIDs(ctx context.Context, scriptIDs []int64) (map[int64][]*model.Line, error) {
	return s.lineModel.BatchGetLinesByScriptIDs(ctx, scriptIDs)
}

// batchGetLineDubbingsForAllScripts 批量获取所有剧本台词的配音数据
func (s *DubbingService) batchGetLineDubbingsForAllScripts(ctx context.Context, lineIDs []int64, userID int64, scriptIDs []int64) (map[int64][]*model.Dubbing, error) {
	if len(lineIDs) == 0 {
		return make(map[int64][]*model.Dubbing), nil
	}

	// 批量获取所有台词的配音
	allDubbingsMap, err := s.dubbingModel.BatchGetLineDubbingsForSimple(ctx, lineIDs, userID)
	if err != nil {
		return nil, err
	}

	// 简化后直接对每个剧本的台词配音应用排序和限制
	for _, scriptID := range scriptIDs {
		// 对该剧本的台词配音应用排序和限制
		for lineID, dubbings := range allDubbingsMap {
			if len(dubbings) > 0 && dubbings[0].ScriptID == scriptID {
				// 应用排序逻辑并限制数量（只取第一条，这里没有批次配音，传入空切片）
				sortedDubbings := s.applySortingLogicForSimple(dubbings, userID, []int64{}, 1)
				allDubbingsMap[lineID] = sortedDubbings
			}
		}
	}

	return allDubbingsMap, nil
}

// batchGetAllRelatedDataForMultipleScripts 为多个剧本批量获取所有关联数据
func (s *DubbingService) batchGetAllRelatedDataForMultipleScripts(ctx context.Context, userID int64, scriptIDs []int64, allUserIDs, allCharacterIDs, allAssetIDs, allDubbingIDs []int64) (*BatchData, error) {
	batchData := &BatchData{}

	// 使用 errgroup 并发获取数据
	eg, egCtx := errgroup.WithContext(ctx)

	// 获取用户信息
	eg.Go(func() error {
		userCtx, cancel := context.WithTimeout(egCtx, 2*time.Second)
		defer cancel()
		userInfos, err := batchGetUserInfo(userCtx, userID, allUserIDs)
		if err != nil {
			logger.Errorf("batchGetUserInfo error: %v", err)
			batchData.UserInfos = make(map[int64]*svcscript.UserInfo)
		} else {
			batchData.UserInfos = userInfos
		}
		return nil
	})

	// 获取角色信息
	eg.Go(func() error {
		characters, err := s.characterModel.BatchGetCharactersByIDs(egCtx, allCharacterIDs)
		if err != nil {
			logger.Errorf("BatchGetCharactersByIDs error: %v", err)
			batchData.Characters = make(map[int64]*model.Character)
		} else {
			batchData.Characters = characters
		}
		return nil
	})

	// 获取角色资源包
	eg.Go(func() error {
		characterAssets, err := s.characterAssetModel.GetAssetsMapByIDs(egCtx, allAssetIDs)
		if err != nil {
			logger.Errorf("GetAssetsMapByIDs error: %v", err)
			batchData.CharacterAssets = make(map[int64]*model.CharacterAsset)
		} else {
			batchData.CharacterAssets = characterAssets
		}
		return nil
	})

	// 获取评论数量
	eg.Go(func() error {
		commentCounts, err := s.commentModel.BatchGetCommentCountByDubbingIDs(egCtx, userID, allDubbingIDs)
		if err != nil {
			logger.Errorf("BatchGetCommentCountByDubbingIDs error: %v", err)
			batchData.CommentCounts = make(map[int64]int32)
		} else {
			batchData.CommentCounts = commentCounts
		}
		return nil
	})

	// 获取用户点赞状态
	if userID > 0 {
		eg.Go(func() error {
			userLikedStatus, err := s.likeModel.BatchIsLiked(egCtx, userID, allDubbingIDs, svcscript.LikeType_LIKE_TYPE_DUBBING)
			if err != nil {
				logger.Errorf("BatchIsLiked error: %v", err)
				batchData.UserLikedStatus = make(map[int64]bool)
			} else {
				batchData.UserLikedStatus = userLikedStatus
			}
			return nil
		})
	} else {
		batchData.UserLikedStatus = make(map[int64]bool)
	}

	// 获取配音完成状态（为多个剧本）
	eg.Go(func() error {
		// 构建需要查询的用户ID列表（去重）
		userIDSet := make(map[int64]struct{})
		for _, userID := range allUserIDs {
			userIDSet[userID] = struct{}{}
		}

		userIDs := make([]int64, 0, len(userIDSet))
		for uID := range userIDSet {
			userIDs = append(userIDs, uID)
		}

		// 为每个剧本获取完成状态
		completionStatusMap := make(map[string]bool)
		for _, scriptID := range scriptIDs {
			userCompletedCharacters, err := s.dubbingCompletionModel.BatchGetCompletedCharacterIDs(egCtx, userIDs, scriptID)
			if err != nil {
				logger.Warnf("BatchGetCompletedCharacterIDs error: %v, scriptID: %d", err, scriptID)
				continue
			}

			// 构建完成状态映射
			for _, userID := range allUserIDs {
				for _, characterID := range allCharacterIDs {
					key := fmt.Sprintf("%d_%d", userID, characterID)
					isComplete := false

					if completedCharacterIDs, exists := userCompletedCharacters[userID]; exists {
						for _, completedCharID := range completedCharacterIDs {
							if completedCharID == characterID {
								isComplete = true
								break
							}
						}
					}
					completionStatusMap[key] = isComplete
				}
			}
		}
		batchData.CompletionStatusMap = completionStatusMap
		return nil
	})

	// 等待所有查询完成
	if err := eg.Wait(); err != nil {
		return nil, err
	}

	return batchData, nil
}

// DeleteDubbing 删除配音
func (s *DubbingService) DeleteDubbing(ctx context.Context, req *svcscript.DeleteDubbingReq) error {
	// 1. 获取配音信息
	dubbing, err := s.dubbingModel.GetDubbingByID(ctx, req.DubbingId)
	if err != nil {
		logger.Errorf("DeleteDubbing GetDubbingByID error: %v, dubbingID: %d", err, req.DubbingId)
		return err
	}

	// 2. 获取剧本信息
	script, err := s.scriptModel.GetScriptByID(ctx, dubbing.ScriptID)
	if err != nil {
		logger.Errorf("DeleteDubbing GetScriptByID error: %v, scriptID: %d", err, dubbing.ScriptID)
		return err
	}

	// 3. 检查权限：剧本作者 OR 配音本人可删除 OR AI配音
	if (script.AuthorID != req.UserId && dubbing.UserID != req.UserId) || dubbing.UserID == consts.AIUserID {
		logger.Errorf("DeleteDubbing error: user %d is not the author of script %d, or not the dubbing owner or AI owner %d", req.UserId, dubbing.ScriptID, dubbing.UserID)
		return errcode.ErrPermissionDenied
	}

	// 4. 执行删除（使用事务）
	err = s.baseModel.Transaction(ctx, func(tx *gorm.DB) error {
		// 软删除配音
		err = s.dubbingModel.DeleteDubbingTx(ctx, tx, req.DubbingId)
		if err != nil {
			logger.Errorf("DeleteDubbing DeleteDubbingTx error: %v, dubbingID: %d", err, req.DubbingId)
			return err
		}

		// 减少台词配音数
		if err := s.lineModel.IncreaseVoiceCountTx(ctx, tx, dubbing.LineID, -1); err != nil {
			logger.Errorf("DeleteDubbing IncreaseVoiceCountTx error: %v, lineID: %d", err, dubbing.LineID)
		}

		// 触发配音删除事件，更新用户统计数据
		if s.eventPusher != nil {
			if err := s.eventPusher.PushDubbingDeletedEvent(ctx, dubbing.ScriptID, dubbing.CharacterID, dubbing.UserID); err != nil {
				logger.Errorf("DeleteDubbing PushDubbingDeletedEvent error: %v", err)
			}
		}

		return nil
	})

	if err != nil {
		logger.Errorf("DeleteDubbing transaction error: %v", err)
		return err
	}

	// 事务成功后，异步更新角色配音完成状态
	// 这样可以确保读取到已提交的删除操作，避免事务一致性问题
	util.SafeGo(func() {
		asyncCtx := context.WithoutCancel(ctx)
		if err := s.dubbingCompletionModel.CheckAndUpdateCharacterCompletionStatus(asyncCtx, dubbing.UserID, dubbing.ScriptID, dubbing.CharacterID); err != nil {
			logger.Warnf("异步检查角色配音完成状态失败: %v, userID: %d, scriptID: %d, characterID: %d",
				err, dubbing.UserID, dubbing.ScriptID, dubbing.CharacterID)
		} else {
			logger.Infof("异步更新配音完成状态完成: userID=%d, scriptID=%d, characterID=%d",
				dubbing.UserID, dubbing.ScriptID, dubbing.CharacterID)
		}
	})

	logger.Infof("DeleteDubbing success: dubbingID=%d, userID=%d, scriptID=%d", req.DubbingId, req.UserId, dubbing.ScriptID)
	return nil
}

// SetDubbingTop 设置配音置顶状态
func (s *DubbingService) SetDubbingTop(ctx context.Context, req *svcscript.SetDubbingTopReq) error {
	// 1. 获取配音信息
	dubbing, err := s.dubbingModel.GetDubbingByID(ctx, req.DubbingId)
	if err != nil {
		logger.Errorf("SetDubbingTop GetDubbingByID error: %v, dubbingID: %d", err, req.DubbingId)
		return err
	}

	// 2. 检查配音状态（已删除或被拒绝的配音不可置顶）
	if req.IsTop {
		// 检查配音是否已删除
		if dubbing.Status == svcscript.Status_STATUS_DELETED {
			logger.Errorf("SetDubbingTop error: dubbing %d is deleted", req.DubbingId)
			return errcode.ErrDubbingSetTopFailed
		}

		// 检查配音是否被机审拒绝或人审拒绝
		if dubbing.ReviewStatus == svcscript.ReviewStatus_REVIEW_STATUS_AUTO_REJECTED ||
			dubbing.ReviewStatus == svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_REJECTED {
			logger.Errorf("SetDubbingTop error: dubbing %d is rejected, reviewStatus: %v", req.DubbingId, dubbing.ReviewStatus)
			return errcode.ErrDubbingSetTopFailed
		}
	}

	// 3. 获取剧本信息
	script, err := s.scriptModel.GetScriptByID(ctx, dubbing.ScriptID)
	if err != nil {
		logger.Errorf("SetDubbingTop GetScriptByID error: %v, scriptID: %d", err, dubbing.ScriptID)
		return err
	}

	// 4. 检查权限：仅剧本作者可操作
	if script.AuthorID != req.UserId {
		logger.Errorf("SetDubbingTop error: user %d is not the author of script %d", req.UserId, dubbing.ScriptID)
		return errcode.ErrUnauthorized
	}

	// 5. 设置置顶状态（同一台词下只能有一个置顶配音）
	err = s.dubbingModel.SetDubbingTopWithExclusive(ctx, req.DubbingId, dubbing.LineID, req.IsTop)
	if err != nil {
		logger.Errorf("SetDubbingTop SetDubbingTopWithExclusive error: %v, dubbingID: %d", err, req.DubbingId)
		return err
	}

	logger.Infof("SetDubbingTop success: dubbingID=%d, userID=%d, scriptID=%d, isTop=%v",
		req.DubbingId, req.UserId, dubbing.ScriptID, req.IsTop)
	return nil
}

// AdminAuditDubbing 管理员审核配音
func (s *DubbingService) AdminAuditDubbing(ctx context.Context, dubbingID int64, reviewStatus svcscript.ReviewStatus, rejectReason string) error {
	// 检查配音是否存在
	dubbing, err := s.dubbingModel.GetDubbingByID(ctx, dubbingID)
	if err != nil {
		logger.Errorf("AdminAuditDubbing GetDubbingByID error: %v, dubbingID: %d", err, dubbingID)
		return err
	}

	// 更新审核状态
	dubbing.ReviewStatus = reviewStatus
	dubbing.UpdatedAt = util.NowTimeMillis()

	// 执行更新
	err = s.dubbingModel.UpdateDubbing(ctx, dubbing)
	if err != nil {
		logger.Errorf("AdminAuditDubbing UpdateDubbing error: %v, dubbingID: %d", err, dubbingID)
		return err
	}

	if reviewStatus == svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED {
		s.NotifyDubbing(ctx, nil, dubbingID)
	}

	return nil
}

// ManualUploadDubbing 手动上传配音
func (s *DubbingService) ManualUploadDubbing(ctx context.Context, req *svcscript.ManualUploadDubbingReq) (int64, error) {
	if err := req.Validate(); err != nil {
		return 0, errcode.ErrorParam
	}

	// 获取台词信息
	line, err := s.lineModel.GetLineByID(ctx, req.LineId)
	if err != nil {
		logger.Errorf("获取台词信息失败: %v, lineID: %d", err, req.LineId)
		return 0, errcode.ErrLineNotFound
	}

	// 获取角色信息
	_, err = s.characterModel.GetCharacterByID(ctx, req.CharacterId)
	if err != nil {
		logger.Errorf("获取角色信息失败: %v, characterID: %d", err, req.CharacterId)
		return 0, errcode.ErrCharacterNotFound
	}

	// 获取角色的随机资源包
	var characterAssetID int64 = 0
	randomAsset, err := s.characterAssetModel.GetRandomAssetByCharacterID(ctx, req.CharacterId)
	if err != nil {
		logger.Warnf("获取角色随机资源包失败: %v, 使用角色ID: %d", err, req.CharacterId)
	} else if randomAsset != nil {
		characterAssetID = randomAsset.ID
		logger.Infof("手动配音使用随机资源包: %d, 角色ID: %d", randomAsset.ID, req.CharacterId)
	}

	// 创建配音记录
	dubbing := &model.Dubbing{
		ScriptID:         line.ScriptID,
		LineID:           req.LineId,
		UserID:           req.UserId, // AI用户ID
		CharacterID:      req.CharacterId,
		CharacterAssetID: characterAssetID, // 使用随机资源包ID
		OriginalURL:      "",               // 手动上传没有原声
		OriginalDuration: 0,
		DubbedURL:        req.DubbedUrl,
		DubbedDuration:   req.DubbedDuration,
		IsAuthor:         false,
		IsTop:            false,
		Status:           svcscript.Status_STATUS_ACTIVE,
		ReviewStatus:     svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED, // 手动上传直接通过审核
		CreatedAt:        util.NowTimeMillis(),
		UpdatedAt:        util.NowTimeMillis(),
	}

	// 使用事务创建配音
	var dubbingID int64
	err = s.baseModel.Transaction(ctx, func(tx *gorm.DB) error {
		err := s.dubbingModel.CreateDubbingTx(ctx, tx, dubbing)
		if err != nil {
			logger.Errorf("创建手动配音失败: %v", err)
			return err
		}
		dubbingID = dubbing.ID
		return nil
	})

	if err != nil {
		return 0, err
	}

	logger.Infof("手动上传配音成功: dubbingID=%d, lineID=%d, characterID=%d, userID=%d",
		dubbingID, req.LineId, req.CharacterId, req.UserId)

	// 手动配音创建指标上报
	if err := metric.ReportPv("dubbing.create.count", 1, nil); err != nil {
		logger.Errorf("Report manual dubbing create metric error: %v", err)
	}

	return dubbingID, nil
}
