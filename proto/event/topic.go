package event

import "new-gitlab.xunlei.cn/vcproject/backends/baselib/nsqutil/event"

const (
	TopicUser   = "vc-user-event"   // 用户
	TopicASR    = "vc-asr-event"    // ASR，语音转文本（动态/评论）
	TopicReport = "vc-report-event" // 上报
	TopicSystem = "vc-system-event" // 系统
	TopicScript = "vc-script-event" // 剧本
	TopicTTS    = "vc-tts-event"    // TTS，文本转语音（台词）
)

const (
	OTypeASR          event.OType = "asr"    // 动态
	OTypeReport       event.OType = "report" // 上报
	OTypeSystem       event.OType = "system" // 注册
	OTypeUser         event.OType = "user"   // 用户
	OTypeScript       event.OType = "script" // 剧本
	OTypeScriptReview event.OType = "script_review"

	OTypeTTSInput  event.OType = "tts_input"  // 文本转语音输入
	OTypeTTSOutput event.OType = "tts_output" // 文本转语音输出
	OTypeVcInput   event.OType = "vc_input"   // 变声输入
)

// -- 事件类型 --

// 系统相关
const (
	ETypeAppReport event.EType = "app_report" // app, h5上报
	ETypeRpcReport event.EType = "rpc_report" // rpc上报
)

// ASR相关
const (
	ETypeMomentAsr          event.EType = "moment_asr"            //动态
	ETypeScriptCommentAsr   event.EType = "script_comment_asr"    //剧本评论
	ETypeSpeechToTextStatus event.EType = "speech_to_text_status" //asr回调
)

// 用户相关
const (
	ETypeSignup   event.EType = "sign_up"   // 注册
	ETypeEditUser event.EType = "edit_user" // 用户编辑
	ETypeFollow   event.EType = "follow"    // 关注
	ETypeUnFollow event.EType = "unfollow"  // 取消关注
)

// 剧本相关
const (
	ETypeLikeCreated         event.EType = "like_created"          //点赞创建
	ETypeLikeCanceled        event.EType = "like_canceled"         //点赞取消
	ETypeDubbingCreated      event.EType = "dubbing_created"       //配音创建
	ETypeDubbingDeleted      event.EType = "dubbing_deleted"       //配音删除
	ETypeScriptCreated       event.EType = "script_created"        //剧本创建
	ETypeScriptDeleted       event.EType = "script_deleted"        //剧本删除
	ETypeCommentCreated      event.EType = "comment_created"       //评论创建
	ETypeCommentDeleted      event.EType = "comment_deleted"       //评论删除
	ETypeScriptFollowed      event.EType = "script_followed"       //剧本关注
	ETypeScriptUnFollowed    event.EType = "script_unfollow"       //剧本取消关注
	ETypeScriptShared        event.EType = "script_shared"         //剧本分享
	ETypeScriptViewed        event.EType = "script_view"           //剧本浏览
	ETypeScriptTopicViewed   event.EType = "script_topic_view"     //剧本话题浏览
	ETypeScriptReview        event.EType = "script_review"         //剧本审核
	ETypeScriptReviewComment event.EType = "script_review_comment" //剧本评论审核
	ETypeScriptReviewDubbing event.EType = "script_review_dubbing" //剧本配音审核
)

// 文本转语音相关
const (
	ETypeTTSInput  event.EType = "tts_input"  // 文本转语音输入
	ETypeTTSOutput event.EType = "tts_output" // 文本转语音输出
	ETypeRvcInput  event.EType = "rvc_input"  // 文本转语音失败
)
