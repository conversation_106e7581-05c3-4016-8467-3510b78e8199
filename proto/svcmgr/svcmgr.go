package svcmgr

import (
	"google.golang.org/grpc"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/grpccli"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/basemsgtransfer"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcactivity"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcconfig"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcmoment"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcreview"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcstat"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcvc"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/vcxxjob"
	"new-gitlab.xunlei.cn/vcproject/backends/svcstat/rcv"
)

var container *serviceContainer

type serviceContainer struct {
	localConn             *grpc.ClientConn
	eventReportClient     rcv.RcvClient
	accountClient         svcaccount.SClient
	momentClient          svcmoment.SClient
	vcClient              svcvc.SClient
	statClient            svcstat.SClient
	activityClient        svcactivity.SClient
	scriptClient          svcscript.SClient
	basemsgtransferClient basemsgtransfer.SClient
	reviewClient          svcreview.SClient
	configClient          svcconfig.SClient
	vcxxjobClient         vcxxjob.SClient
}

func InitServices() {
	logger.Infof("InitServices container=%+v", container)
	if container == nil {
		var localConn *grpc.ClientConn
		localConn, err := grpccli.NewLocalConn()
		if err != nil {
			logger.Panicf("NewLocalConn error %v", err)
			return
		}
		container = &serviceContainer{
			localConn:             localConn,
			accountClient:         svcaccount.NewSClient(localConn),
			momentClient:          svcmoment.NewSClient(localConn),
			vcClient:              svcvc.NewSClient(localConn),
			statClient:            svcstat.NewSClient(localConn),
			activityClient:        svcactivity.NewSClient(localConn),
			scriptClient:          svcscript.NewSClient(localConn),
			basemsgtransferClient: basemsgtransfer.NewSClient(localConn),
			reviewClient:          svcreview.NewSClient(localConn),
			configClient:          svcconfig.NewSClient(localConn),
			vcxxjobClient:         vcxxjob.NewSClient(localConn),
		}
		//https://gitlab.xunlei.cn/data-driven/examples
		remoteConn, err := grpccli.NewRemoteConn("analysis-rcv.xldc.svc:50052")
		if err != nil {
			logger.Panicf("NewRemoteConn error %v", err)
			return
		}
		container.eventReportClient = rcv.NewRcvClient(remoteConn)

		// 初始化token验证器
		InitTokenValidator()
	}
}

func AccountClient() svcaccount.SClient {
	return container.accountClient
}

func MomentClient() svcmoment.SClient {
	return container.momentClient
}

func VcClient() svcvc.SClient {
	return container.vcClient
}

func EventReportClient() rcv.RcvClient {
	return container.eventReportClient
}

func StatClient() svcstat.SClient {
	return container.statClient
}

func ActivityClient() svcactivity.SClient {
	return container.activityClient
}

func ScriptClient() svcscript.SClient {
	return container.scriptClient
}

func BaseMsgTransferClient() basemsgtransfer.SClient {
	return container.basemsgtransferClient
}

func ReviewClient() svcreview.SClient {
	return container.reviewClient
}

func ConfigClient() svcconfig.SClient {
	return container.configClient
}

func VcxxjobClient() vcxxjob.SClient {
	return container.vcxxjobClient
}
