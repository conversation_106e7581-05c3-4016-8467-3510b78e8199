package scripts

import "github.com/flipped-aurora/gin-vue-admin/server/service"

type ApiGroup struct {
	ScriptApi
	TopicApi
	CoverApi
	LineApi
	DubbingApi
	CommentApi
	ScriptTopicRelationApi
	ScriptCharacterRelationApi
	ReportApi
}

var (
	scriptService                  = service.ServiceGroupApp.ScriptsServiceGroup.ScriptService
	topicService                   = service.ServiceGroupApp.ScriptsServiceGroup.TopicService
	coverService                   = service.ServiceGroupApp.ScriptsServiceGroup.CoverService
	lineService                    = service.ServiceGroupApp.ScriptsServiceGroup.LineService
	dubbingService                 = service.ServiceGroupApp.ScriptsServiceGroup.DubbingService
	commentService                 = service.ServiceGroupApp.ScriptsServiceGroup.CommentService
	scriptTopicRelationService     = service.ServiceGroupApp.ScriptsServiceGroup.ScriptTopicRelationService
	scriptCharacterRelationService = service.ServiceGroupApp.ScriptsServiceGroup.ScriptCharacterRelationService
	characterService               = service.ServiceGroupApp.ScriptsServiceGroup.CharacterService
	characterAssetsService         = service.ServiceGroupApp.ScriptsServiceGroup.CharacterAssetsService
	reportService                  = service.ServiceGroupApp.ScriptsServiceGroup.ReportService
)
