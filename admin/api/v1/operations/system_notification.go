package operations

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/operations/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcchat"
)

type SystemNotificationApi struct{}

// SendSystemNotification 发送系统通知
// @Tags SystemNotification
// @Summary 发送系统通知
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SendSystemNotificationRequest true "发送系统通知请求"
// @Success 200 {object} response.Response{msg=string} "发送成功"
// @Router /systemNotification/sendSystemNotification [post]
func (s *SystemNotificationApi) SendSystemNotification(c *gin.Context) {
	var req request.SendSystemNotificationRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = systemNotificationService.SendSystemNotification(c, req)
	if err != nil {
		global.GVA_LOG.Error("发送系统通知失败!", zap.Error(err))
		response.FailWithMessage("发送系统通知失败", c)
		return
	}
	response.OkWithMessage("发送系统通知成功", c)
}

// GetTemplateJumpTypes 获取跳转类型枚举
// @Tags SystemNotification
// @Summary 获取跳转类型枚举
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]interface{}} "获取成功"
// @Router /systemNotification/getTemplateJumpTypes [get]
func (s *SystemNotificationApi) GetTemplateJumpTypes(c *gin.Context) {
	jumpTypes := map[string]interface{}{
		"jump_type_init":   int32(svcchat.TemplateJumpType_jump_type_init),
		"jump_web":         int32(svcchat.TemplateJumpType_jump_web),
		"jump_page":        int32(svcchat.TemplateJumpType_jump_page),
		"jump_update_app":  int32(svcchat.TemplateJumpType_jump_update_app),
		"jump_session_top": int32(svcchat.TemplateJumpType_jump_session_top),
		"jump_script":      int32(svcchat.TemplateJumpType_jump_script),
		"jump_user_info":   int32(svcchat.TemplateJumpType_jump_user_info),
	}
	
	response.OkWithDetailed(gin.H{"jumpTypes": jumpTypes}, "获取跳转类型成功", c)
}

// GetSystemNotificationRecords 获取系统通知记录列表
// @Tags SystemNotification
// @Summary 获取系统通知记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.GetSystemNotificationRecordsRequest true "获取系统通知记录列表请求"
// @Success 200 {object} response.Response{data=response.GetSystemNotificationRecordsResponse} "获取成功"
// @Router /systemNotification/getSystemNotificationRecords [get]
func (s *SystemNotificationApi) GetSystemNotificationRecords(c *gin.Context) {
	var req request.GetSystemNotificationRecordsRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	list, err := systemNotificationService.GetSystemNotificationRecords(req)
	if err != nil {
		global.GVA_LOG.Error("获取系统通知记录列表失败!", zap.Error(err))
		response.FailWithMessage("获取系统通知记录列表失败", c)
		return
	}
	response.OkWithDetailed(list, "获取系统通知记录列表成功", c)
}
