package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/router"
	"github.com/gin-gonic/gin"
)

func holder(routers ...*gin.RouterGroup) {
	_ = routers
	_ = router.RouterGroupApp
}
func initBizRouter(routers ...*gin.RouterGroup) {
	privateGroup := routers[0]
	publicGroup := routers[1]
	holder(publicGroup, privateGroup)
	{
		mymomentsRouter := router.RouterGroupApp.Mymoments
		mymomentsRouter.InitMomentsRouter(privateGroup, publicGroup)
	}
	{
		charactersRouter := router.RouterGroupApp.Characters
		charactersRouter.InitCharacterRouter(privateGroup, publicGroup)
		charactersRouter.InitCharacterAssetsRouter(privateGroup, publicGroup)
	}
	{
		ipsRouter := router.RouterGroupApp.Ips
		ipsRouter.InitIpsRouter(privateGroup, publicGroup)
	}
	{
		scriptsRouter := router.RouterGroupApp.Scripts
		scriptsRouter.InitScriptRouter(privateGroup, publicGroup)
		scriptsRouter.InitTopicRouter(privateGroup, publicGroup)
		scriptsRouter.InitCoverRouter(privateGroup, publicGroup)
		scriptsRouter.InitLineRouter(privateGroup, publicGroup)
		scriptsRouter.InitDubbingRouter(privateGroup, publicGroup)
		scriptsRouter.InitCommentRouter(privateGroup, publicGroup)
		scriptsRouter.InitScriptTopicRelationRouter(privateGroup, publicGroup)
		scriptsRouter.InitScriptCharacterRelationRouter(privateGroup, publicGroup)
		scriptsRouter.InitReportRouter(privateGroup, publicGroup)
	}
	{
		usersRouter := router.RouterGroupApp.Users
		usersRouter.InitUserRouter(privateGroup, publicGroup)
		usersRouter.InitUserScoreLogRouter(privateGroup, publicGroup)
		usersRouter.InitUserAuditRecordRouter(privateGroup, publicGroup)
	}
	{
		operationsRouter := router.RouterGroupApp.Operations
		operationsRouter.InitAppVersionRouter(privateGroup)
		operationsRouter.InitSystemNotificationRouter(privateGroup)
	}
}
